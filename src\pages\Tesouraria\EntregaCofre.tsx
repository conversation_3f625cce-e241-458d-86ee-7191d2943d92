
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Vault, Shield, Lock, CheckCircle } from 'lucide-react';

const EntregaCofre = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega ao Cofre</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores para o cofre principal</p>
        </div>
        <Button className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Confirmar Entrega
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Vault className="h-5 w-5" />
              Valores a Depositar no Cofre
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <div className="flex items-center gap-2 text-blue-700 mb-2">
                <Shield className="h-4 w-4" />
                <span className="font-medium">Área de Segurança Máxima</span>
              </div>
              <p className="text-sm text-blue-600">
                Esta operação registará a entrada de valores no cofre principal da agência.
              </p>
            </div>

            <div className="space-y-3">
              <div>
                <Label htmlFor="valor-total">Valor Total a Depositar</Label>
                <Input 
                  id="valor-total" 
                  type="number" 
                  step="0.01" 
                  placeholder="0,00" 
                  className="text-lg font-bold"
                />
              </div>

              <div>
                <Label htmlFor="origem" className="dark:text-gray-100">Origem dos Valores</Label>
                <select className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
                  <option value="">Selecionar origem...</option>
                  <option value="caixa">Caixa Principal</option>
                  <option value="tesouraria">Tesouraria</option>
                  <option value="deposito">Depósito Cliente</option>
                  <option value="transferencia">Transferência</option>
                  <option value="outros">Outros</option>
                </select>
              </div>

              <div>
                <Label htmlFor="codigo-cofre">Código do Cofre</Label>
                <Input 
                  id="codigo-cofre" 
                  type="password" 
                  placeholder="Digite o código do cofre"
                  className="font-mono"
                />
              </div>

              <div>
                <Label htmlFor="chave-seguranca">Chave de Segurança</Label>
                <Input 
                  id="chave-seguranca" 
                  type="password" 
                  placeholder="Digite a chave de segurança"
                  className="font-mono"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Detalhes da Operação
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Data/Hora:</span>
                <span className="font-bold dark:text-gray-100">{new Date().toLocaleString('pt-PT')}</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Funcionário:</span>
                <span className="font-bold dark:text-gray-100">Gestor (GE)</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Terminal:</span>
                <span className="font-bold dark:text-gray-100">CAIXA-001</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Status do Cofre:</span>
                <span className="font-bold text-green-600">Operacional</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsavel-cofre" className="dark:text-gray-100">Responsável pelo Cofre</Label>
              <Input id="responsavel-cofre" placeholder="Nome do responsável" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacoes-cofre" className="dark:text-gray-100">Observações</Label>
              <textarea
                id="observacoes-cofre"
                className="w-full p-2 border border-gray-300 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600"
                rows={3}
                placeholder="Observações sobre a operação..."
              ></textarea>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-lg">
              <div className="text-yellow-700 text-sm">
                <strong>Atenção:</strong> Esta operação requer dupla autenticação e será registada no sistema de auditoria.
              </div>
            </div>

            <Button className="w-full" size="lg">
              <Vault className="h-4 w-4 mr-2" />
              Depositar no Cofre
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EntregaCofre;
