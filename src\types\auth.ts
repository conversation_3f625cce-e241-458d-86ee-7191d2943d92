// Tipos para o sistema de autenticação

export interface User {
  id: string;
  nome: string;
  email: string;
  perfil: UserRole;
  balcao: string;
  telefone?: string;
  avatar?: string;
  status: 'ativo' | 'inativo' | 'bloqueado';
  ultimoLogin?: Date;
  criadoEm: Date;
}

export type UserRole = 'admin' | 'gerente' | 'caixa' | 'tesoureiro';

export interface Permission {
  module: string;
  actions: string[];
}

export interface RolePermissions {
  [key: string]: Permission[];
}

export interface LoginCredentials {
  email: string;
  senha: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: Permission[];
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  hasPermission: (module: string, action: string) => boolean;
  hasRole: (role: UserRole | UserRole[]) => boolean;
}

// Definição de permissões por role
export const ROLE_PERMISSIONS: RolePermissions = {
  admin: [
    { module: 'sistema', actions: ['read', 'write', 'delete'] },
    { module: 'usuarios', actions: ['read', 'write', 'delete'] },
    { module: 'clientes', actions: ['read', 'write', 'delete'] },
    { module: 'transferencias', actions: ['read', 'write', 'delete'] },
    { module: 'caixa', actions: ['read', 'write', 'delete'] },
    { module: 'tesouraria', actions: ['read', 'write', 'delete'] },
    { module: 'cartoes', actions: ['read', 'write', 'delete'] },
    { module: 'cambios', actions: ['read', 'write', 'delete'] },
    { module: 'seguros', actions: ['read', 'write', 'delete'] },
    { module: 'atm', actions: ['read', 'write', 'delete'] },
    { module: 'relatorios', actions: ['read', 'write'] }
  ],
  gerente: [
    { module: 'clientes', actions: ['read', 'write'] },
    { module: 'transferencias', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read', 'write'] },
    { module: 'tesouraria', actions: ['read', 'write'] },
    { module: 'cartoes', actions: ['read', 'write'] },
    { module: 'cambios', actions: ['read', 'write'] },
    { module: 'seguros', actions: ['read', 'write'] },
    { module: 'atm', actions: ['read'] },
    { module: 'relatorios', actions: ['read'] }
  ],
  caixa: [
    { module: 'clientes', actions: ['read'] },
    { module: 'transferencias', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read', 'write'] },
    { module: 'cambios', actions: ['read', 'write'] }
  ],
  tesoureiro: [
    { module: 'tesouraria', actions: ['read', 'write'] },
    { module: 'caixa', actions: ['read'] },
    { module: 'atm', actions: ['read', 'write'] }
  ]
};
