import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { UserPlus, Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react';

interface FormErrors {
  nome?: string;
  email?: string;
  senha?: string;
  confirmarSenha?: string;
  perfil?: string;
  balcao?: string;
}

const RegistarUsuario = () => {
  const [formData, setFormData] = useState({
    nome: '',
    email: '',
    senha: '',
    confirmarSenha: '',
    perfil: '',
    balcao: '',
    telefone: '',
    observacoes: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();

  const perfis = [
    { value: 'admin', label: 'Administrador' },
    { value: 'gerente', label: 'Gerente' },
    { value: 'caixa', label: 'Operador de Caixa' },
    { value: 'tesoureiro', label: 'Tesoureiro' },
    { value: 'atendimento', label: 'Atendimento ao Cliente' }
  ];

  const balcoes = [
    { value: 'sede', label: 'Sede Principal' },
    { value: 'luanda-centro', label: 'Luanda Centro' },
    { value: 'luanda-sul', label: 'Luanda Sul' },
    { value: 'benguela', label: 'Benguela' },
    { value: 'huambo', label: 'Huambo' }
  ];

  const validarFormulario = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.nome.trim()) {
      newErrors.nome = 'Nome completo é obrigatório';
    } else if (formData.nome.trim().length < 3) {
      newErrors.nome = 'Nome deve ter pelo menos 3 caracteres';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (!formData.senha) {
      newErrors.senha = 'Senha é obrigatória';
    } else if (formData.senha.length < 6) {
      newErrors.senha = 'Senha deve ter pelo menos 6 caracteres';
    }

    if (!formData.confirmarSenha) {
      newErrors.confirmarSenha = 'Confirmação de senha é obrigatória';
    } else if (formData.senha !== formData.confirmarSenha) {
      newErrors.confirmarSenha = 'Senhas não coincidem';
    }

    if (!formData.perfil) {
      newErrors.perfil = 'Perfil de usuário é obrigatório';
    }

    if (!formData.balcao) {
      newErrors.balcao = 'Balcão é obrigatório';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validarFormulario()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simula criação do usuário
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Usuário registrado",
        description: `Usuário ${formData.nome} foi registrado com sucesso`,
      });

      // Limpar formulário
      setFormData({
        nome: '',
        email: '',
        senha: '',
        confirmarSenha: '',
        perfil: '',
        balcao: '',
        telefone: '',
        observacoes: ''
      });
      setErrors({});

    } catch (error) {
      toast({
        title: "Erro no registro",
        description: "Ocorreu um erro ao registrar o usuário. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = formData.nome && formData.email && formData.senha && 
                     formData.confirmarSenha && formData.perfil && formData.balcao && 
                     Object.keys(errors).length === 0;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <UserPlus className="h-8 w-8" />
          Registar Usuário
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">Criar nova conta de usuário no sistema</p>
      </div>

      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informações do Usuário</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Nome Completo */}
            <div className="space-y-2">
              <Label htmlFor="nome">
                Nome Completo <span className="text-red-500">*</span>
              </Label>
              <Input
                id="nome"
                value={formData.nome}
                onChange={(e) => handleInputChange('nome', e.target.value)}
                placeholder="Digite o nome completo"
                className={errors.nome ? 'border-red-500' : ''}
              />
              {errors.nome && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.nome}
                </p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Email <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Senha */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="senha">
                  Senha <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="senha"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.senha}
                    onChange={(e) => handleInputChange('senha', e.target.value)}
                    placeholder="Mínimo 6 caracteres"
                    className={`pr-10 ${errors.senha ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.senha && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.senha}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmarSenha">
                  Confirmar Senha <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="confirmarSenha"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmarSenha}
                    onChange={(e) => handleInputChange('confirmarSenha', e.target.value)}
                    placeholder="Repita a senha"
                    className={`pr-10 ${errors.confirmarSenha ? 'border-red-500' : ''}`}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {errors.confirmarSenha && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.confirmarSenha}
                  </p>
                )}
              </div>
            </div>

            {/* Perfil e Balcão */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="perfil">
                  Perfil de Usuário <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.perfil} onValueChange={(value) => handleInputChange('perfil', value)}>
                  <SelectTrigger className={errors.perfil ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Selecione o perfil" />
                  </SelectTrigger>
                  <SelectContent>
                    {perfis.map((perfil) => (
                      <SelectItem key={perfil.value} value={perfil.value}>
                        {perfil.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.perfil && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.perfil}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="balcao">
                  Balcão <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.balcao} onValueChange={(value) => handleInputChange('balcao', value)}>
                  <SelectTrigger className={errors.balcao ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Selecione o balcão" />
                  </SelectTrigger>
                  <SelectContent>
                    {balcoes.map((balcao) => (
                      <SelectItem key={balcao.value} value={balcao.value}>
                        {balcao.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.balcao && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.balcao}
                  </p>
                )}
              </div>
            </div>

            {/* Telefone (Opcional) */}
            <div className="space-y-2">
              <Label htmlFor="telefone">Telefone (Opcional)</Label>
              <Input
                id="telefone"
                value={formData.telefone}
                onChange={(e) => handleInputChange('telefone', e.target.value)}
                placeholder="+244 900 000 000"
              />
            </div>

            {/* Observações (Opcional) */}
            <div className="space-y-2">
              <Label htmlFor="observacoes">Observações (Opcional)</Label>
              <Input
                id="observacoes"
                value={formData.observacoes}
                onChange={(e) => handleInputChange('observacoes', e.target.value)}
                placeholder="Informações adicionais sobre o usuário"
              />
            </div>

            {/* Resumo */}
            {isFormValid && (
              <Alert className="bg-blue-50 border-blue-200">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <AlertDescription>
                  <div className="space-y-1">
                    <p className="font-medium text-blue-800">Resumo do Usuário:</p>
                    <p className="text-sm text-blue-700"><strong>Nome:</strong> {formData.nome}</p>
                    <p className="text-sm text-blue-700"><strong>Email:</strong> {formData.email}</p>
                    <p className="text-sm text-blue-700"><strong>Perfil:</strong> {perfis.find(p => p.value === formData.perfil)?.label}</p>
                    <p className="text-sm text-blue-700"><strong>Balcão:</strong> {balcoes.find(b => b.value === formData.balcao)?.label}</p>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {/* Botões */}
            <div className="flex gap-4 pt-4">
              <Button 
                type="submit"
                disabled={!isFormValid || isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? 'Registrando...' : 'Registrar Usuário'}
              </Button>
              
              <Button 
                type="button"
                variant="outline" 
                onClick={() => {
                  setFormData({
                    nome: '',
                    email: '',
                    senha: '',
                    confirmarSenha: '',
                    perfil: '',
                    balcao: '',
                    telefone: '',
                    observacoes: ''
                  });
                  setErrors({});
                }}
                className="px-8"
              >
                Limpar
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
};

export default RegistarUsuario;
