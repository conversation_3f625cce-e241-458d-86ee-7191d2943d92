import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { HandCoins, Plus, Eye, FileText } from 'lucide-react';

interface EntregaTesoureiro {
  id: number;
  data: string;
  tesoureiro: string;
  valorEntregue: number;
  moeda: string;
  observacoes: string;
  status: 'pendente' | 'confirmada' | 'rejeitada';
  responsavel: string;
}

const EntregaTesoureiro = () => {
  const [entregas, setEntregas] = useState<EntregaTesoureiro[]>([
    {
      id: 1,
      data: '2025-06-16',
      tesoureiro: 'Ana Paula Santos',
      valorEntregue: 50000000,
      moeda: 'AOA',
      observacoes: 'Entrega regular do turno da manhã',
      status: 'confirmada',
      responsavel: 'João Manuel Silva'
    },
    {
      id: 2,
      data: '2025-06-16',
      tesoureiro: 'Carlos Eduardo Lima',
      valorEntregue: 25000000,
      moeda: 'AOA',
      observacoes: 'Entrega parcial - aguardando complemento',
      status: 'pendente',
      responsavel: 'Maria Fernanda Costa'
    }
  ]);

  const [novaEntrega, setNovaEntrega] = useState({
    tesoureiro: '',
    valorEntregue: '',
    moeda: 'AOA',
    observacoes: ''
  });

  const [isModalOpen, setIsModalOpen] = useState(false);

  const { toast } = useToast();

  const tesoureiros = [
    'Ana Paula Santos',
    'Carlos Eduardo Lima',
    'Pedro Miguel Ferreira',
    'Luisa Maria Gonçalves'
  ];

  const moedas = [
    { value: 'AOA', label: 'Kwanza (AOA)' },
    { value: 'USD', label: 'Dólar Americano (USD)' },
    { value: 'EUR', label: 'Euro (EUR)' }
  ];

  const formatarValor = (valor: number, moeda: string) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: moeda === 'AOA' ? 'AOA' : moeda,
      minimumFractionDigits: 2
    }).format(valor);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmada':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Confirmada</Badge>;
      case 'pendente':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pendente</Badge>;
      case 'rejeitada':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Rejeitada</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAdicionarEntrega = () => {
    if (!novaEntrega.tesoureiro || !novaEntrega.valorEntregue) {
      toast({
        title: "Campos obrigatórios",
        description: "Preencha o tesoureiro e valor da entrega",
        variant: "destructive"
      });
      return;
    }

    const entrega: EntregaTesoureiro = {
      id: Date.now(),
      data: new Date().toISOString().split('T')[0],
      tesoureiro: novaEntrega.tesoureiro,
      valorEntregue: parseFloat(novaEntrega.valorEntregue.replace(/[^\d,]/g, '').replace(',', '.')),
      moeda: novaEntrega.moeda,
      observacoes: novaEntrega.observacoes,
      status: 'pendente',
      responsavel: 'Sistema'
    };

    setEntregas([entrega, ...entregas]);
    setNovaEntrega({
      tesoureiro: '',
      valorEntregue: '',
      moeda: 'AOA',
      observacoes: ''
    });
    setIsModalOpen(false);

    toast({
      title: "Entrega registrada",
      description: "A entrega foi registrada com sucesso",
    });
  };

  const handleAlterarStatus = (id: number, novoStatus: string) => {
    setEntregas(entregas.map(e => 
      e.id === id ? { ...e, status: novoStatus as 'pendente' | 'confirmada' | 'rejeitada' } : e
    ));
    
    toast({
      title: "Status atualizado",
      description: `Entrega ${novoStatus}`,
    });
  };

  const formatarValorInput = (value: string) => {
    const numericValue = value.replace(/[^\d]/g, '');
    if (!numericValue) return '';
    
    const number = parseInt(numericValue);
    return new Intl.NumberFormat('pt-AO').format(number);
  };

  return (
    <div className="space-y-6 content-container">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <HandCoins className="h-8 w-8" />
            Entrega ao Tesoureiro
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Registrar e gerenciar entregas de valores pelos tesoureiros</p>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogTrigger asChild>
            <Button size="lg" className="bg-twins-primary hover:bg-twins-secondary text-white shadow-lg">
              <Plus className="h-5 w-5 mr-2" />
              Nova Entrega
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Nova Entrega ao Tesoureiro
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="tesoureiro">
                  Tesoureiro <span className="text-red-500">*</span>
                </Label>
                <Select value={novaEntrega.tesoureiro} onValueChange={(value) => setNovaEntrega(prev => ({ ...prev, tesoureiro: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o tesoureiro" />
                  </SelectTrigger>
                  <SelectContent>
                    {tesoureiros.map((tesoureiro) => (
                      <SelectItem key={tesoureiro} value={tesoureiro}>
                        {tesoureiro}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="moeda">Moeda</Label>
                <Select value={novaEntrega.moeda} onValueChange={(value) => setNovaEntrega(prev => ({ ...prev, moeda: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {moedas.map((moeda) => (
                      <SelectItem key={moeda.value} value={moeda.value}>
                        {moeda.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="valorEntregue">
                  Valor Entregue <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="valorEntregue"
                  value={novaEntrega.valorEntregue}
                  onChange={(e) => setNovaEntrega(prev => ({ ...prev, valorEntregue: formatarValorInput(e.target.value) }))}
                  placeholder="0"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Input
                  id="observacoes"
                  value={novaEntrega.observacoes}
                  onChange={(e) => setNovaEntrega(prev => ({ ...prev, observacoes: e.target.value }))}
                  placeholder="Observações sobre a entrega..."
                />
              </div>

              <Button onClick={handleAdicionarEntrega} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Registrar Entrega
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Cards de Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Total Confirmado */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Total Confirmado</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center p-6 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                {formatarValor(entregas.filter(e => e.status === 'confirmada').reduce((total, e) => total + e.valorEntregue, 0), 'AOA')}
              </div>
              <div className="text-sm text-green-700 dark:text-green-300 mt-2">
                Valor total das entregas confirmadas hoje
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Entregas Pendentes */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg">Entregas Pendentes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center p-6 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div className="text-3xl font-bold text-yellow-600 dark:text-yellow-400">
                {entregas.filter(e => e.status === 'pendente').length}
              </div>
              <div className="text-sm text-yellow-700 dark:text-yellow-300 mt-2">
                Entregas aguardando confirmação
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Entregas */}
      <Card>
            <CardHeader>
              <CardTitle>Entregas Registradas ({entregas.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="table-container rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Data</TableHead>
                      <TableHead>Tesoureiro</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Ações</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {entregas.map((entrega) => (
                      <TableRow key={entrega.id}>
                        <TableCell>
                          {new Date(entrega.data).toLocaleDateString('pt-AO')}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{entrega.tesoureiro}</div>
                            {entrega.observacoes && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {entrega.observacoes}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatarValor(entrega.valorEntregue, entrega.moeda)}
                        </TableCell>
                        <TableCell>
                          <Select value={entrega.status} onValueChange={(value) => handleAlterarStatus(entrega.id, value)}>
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="pendente">Pendente</SelectItem>
                              <SelectItem value="confirmada">Confirmada</SelectItem>
                              <SelectItem value="rejeitada">Rejeitada</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0"
                            >
                              <FileText className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
    </div>
  );
};

export default EntregaTesoureiro;
