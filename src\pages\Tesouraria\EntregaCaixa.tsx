
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Co<PERSON>, Calculator, CheckCircle } from 'lucide-react';

const EntregaCaixa = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Entrega a Caixa</h1>
          <p className="text-gray-600 dark:text-gray-400">Registar entrega de valores da tesouraria para o caixa</p>
        </div>
        <Button className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Confirmar Entrega
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Coins className="h-5 w-5" />
              Denominações a Entregar
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="notas-10000">Notas de 10.000 Kz</Label>
                <Input id="notas-10000" type="number" placeholder="Quantidade" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <Label htmlFor="valor-10000" className="dark:text-gray-100">Valor Total</Label>
                <Input id="valor-10000" value="0,00 Kz" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="notas-5000">Notas de 5.000 Kz</Label>
                <Input id="notas-5000" type="number" placeholder="Quantidade" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <Label htmlFor="valor-5000" className="dark:text-gray-100">Valor Total</Label>
                <Input id="valor-5000" value="0,00 Kz" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="notas-2000">Notas de 2.000 Kz</Label>
                <Input id="notas-2000" type="number" placeholder="Quantidade" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <Label htmlFor="valor-2000" className="dark:text-gray-100">Valor Total</Label>
                <Input id="valor-2000" value="0,00 Kz" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="notas-1000">Notas de 1.000 Kz</Label>
                <Input id="notas-1000" type="number" placeholder="Quantidade" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <Label htmlFor="valor-1000" className="dark:text-gray-100">Valor Total</Label>
                <Input id="valor-1000" value="0,00 Kz" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="notas-500">Notas de 500 Kz</Label>
                <Input id="notas-500" type="number" placeholder="Quantidade" className="dark:bg-gray-700 dark:text-gray-100" />
              </div>
              <div>
                <Label htmlFor="valor-500" className="dark:text-gray-100">Valor Total</Label>
                <Input id="valor-500" value="0,00 Kz" readOnly className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="outras">Outras Denominações</Label>
                <Input id="outras" type="number" step="0.01" placeholder="Valor em Kz" />
              </div>
              <div>
                <Label htmlFor="moedas">Moedas</Label>
                <Input id="moedas" type="number" step="0.01" placeholder="Valor em Kz" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Resumo da Entrega
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Notas de 10.000 Kz:</span>
                <span className="font-bold dark:text-gray-100">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Notas de 5.000 Kz:</span>
                <span className="font-bold dark:text-gray-100">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Notas de 2.000 Kz:</span>
                <span className="font-bold dark:text-gray-100">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Notas de 1.000 Kz:</span>
                <span className="font-bold dark:text-gray-100">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span className="dark:text-gray-100">Notas de 500 Kz:</span>
                <span className="font-bold dark:text-gray-100">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Outras denominações:</span>
                <span className="font-bold">0,00 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Moedas:</span>
                <span className="font-bold">0,00 Kz</span>
              </div>
              <hr />
              <div className="flex justify-between text-lg font-bold text-primary">
                <span>Total a Entregar:</span>
                <span>0,00 Kz</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsavel" className="dark:text-gray-100">Responsável pela Entrega</Label>
              <Input id="responsavel" placeholder="Nome do funcionário" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="observacoes">Observações</Label>
              <Input id="observacoes" placeholder="Observações adicionais (opcional)" />
            </div>

            <Button className="w-full" size="lg">
              <CheckCircle className="h-4 w-4 mr-2" />
              Registar Entrega
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EntregaCaixa;
