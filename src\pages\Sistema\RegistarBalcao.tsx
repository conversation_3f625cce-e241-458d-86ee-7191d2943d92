
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pencil, Trash2 } from 'lucide-react';
import ActionMenu, { ActionMenuItem } from '@/components/ui/ActionMenu';

interface Balcao {
  id: number;
  nome: string;
  valor: string;
  provincia: string;
  municipio: string;
}

const RegistarBalcao = () => {
  const [nomeBalcao, setNomeBalcao] = useState('');
  const [provincia, setProvincia] = useState('');
  const [municipio, setMunicipio] = useState('');
  const [showEntries, setShowEntries] = useState('1');
  const [searchTerm, setSearchTerm] = useState('');

  const [balcoes] = useState<Balcao[]>([
    { id: 1, nome: 'Andre', valor: '300.000.000,00', provincia: 'luanda', municipio: 'belas' },
    { id: 2, nome: 'Banco Twins Zango 5 mil', valor: '300.000.000,00', provincia: 'Luanda', municipio: 'colibengo' },
    { id: 3, nome: 'MAKUBU ALBINO', valor: '300.000.000,00', provincia: 'Luanda', municipio: 'viana' },
    { id: 4, nome: 'Marcio Balcao', valor: '300.000.000,00', provincia: 'Luanda', municipio: 'Luanda' },
    { id: 5, nome: 'TWINS BANK Maianga', valor: '300.000.000,00', provincia: 'Luanda', municipio: 'Luanda' },
    { id: 6, nome: 'TWINS BANK-BENFICA', valor: '300.000.000,00', provincia: 'LUANDA', municipio: 'TALATONA' }
  ]);

  const handleAdicionarBalcao = () => {
    console.log('Adicionando balcão:', {
      nome: nomeBalcao,
      provincia,
      municipio
    });
    // Lógica para adicionar balcão
  };

  const handleEditBalcao = (id: number) => {
    console.log('Editando balcão:', id);
    // Lógica para editar balcão
  };

  const handleDeleteBalcao = (id: number) => {
    console.log('Deletando balcão:', id);
    // Lógica para deletar balcão
  };

  const filteredBalcoes = balcoes.filter(balcao => 
    balcao.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balcao.provincia.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balcao.municipio.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Registar Balcão</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lista de Balcões */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold flex items-center">
                  Lista Balcões <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full ml-2">10</span>
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {/* Controles da tabela */}
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm">Show</span>
                  <Select value={showEntries} onValueChange={setShowEntries}>
                    <SelectTrigger className="w-16">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm">entries</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="text-sm">Search:</span>
                  <Input
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder=""
                    className="w-32"
                  />
                </div>
              </div>

              {/* Tabela */}
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nome Balcao</TableHead>
                    <TableHead>Valor</TableHead>
                    <TableHead>Provincia</TableHead>
                    <TableHead>Municipio</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBalcoes.slice(0, parseInt(showEntries)).map((balcao) => (
                    <TableRow key={balcao.id}>
                      <TableCell className="font-medium">{balcao.nome}</TableCell>
                      <TableCell>{balcao.valor}</TableCell>
                      <TableCell>{balcao.provincia}</TableCell>
                      <TableCell>{balcao.municipio}</TableCell>
                      <TableCell>
                        <ActionMenu
                          items={[
                            {
                              label: 'Editar',
                              icon: Pencil,
                              onClick: () => handleEditBalcao(balcao.id)
                            },
                            {
                              label: 'Excluir',
                              icon: Trash2,
                              onClick: () => handleDeleteBalcao(balcao.id),
                              variant: 'destructive'
                            }
                          ]}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>

        {/* Adicionar Balcao */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">Adicionar balcao</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="nome-balcao">
                  Nome Balcao <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="nome-balcao"
                  value={nomeBalcao}
                  onChange={(e) => setNomeBalcao(e.target.value)}
                  placeholder="Digite o nome do balcão"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="provincia">
                  Provincia <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="provincia"
                  value={provincia}
                  onChange={(e) => setProvincia(e.target.value)}
                  placeholder="Digite a província"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="municipio">
                  Municipio <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="municipio"
                  value={municipio}
                  onChange={(e) => setMunicipio(e.target.value)}
                  placeholder="Digite o município"
                />
              </div>

              <Button 
                onClick={handleAdicionarBalcao}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white"
              >
                Adicionar Balcao
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RegistarBalcao;
