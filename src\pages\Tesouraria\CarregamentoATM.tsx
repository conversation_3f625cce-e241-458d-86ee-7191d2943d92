
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Banknote, MapPin, Battery, Wifi } from 'lucide-react';

const CarregamentoATM = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Carregamento ATM</h1>
          <p className="text-gray-600 dark:text-gray-400">Gestão e carregamento dos caixas automáticos</p>
        </div>
        <Button className="flex items-center gap-2">
          <Banknote className="h-4 w-4" />
          Novo Carregamento
        </Button>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Principal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="text-green-600 font-bold">Online</span>
              </div>
              <div className="flex justify-between">
                <span>Saldo Atual:</span>
                <span className="font-bold">40.707.000 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Capacidade:</span>
                <span className="font-bold">90.000.000 Kz</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-green-600" />
              </div>
              <Button className="w-full mt-4">Carregar ATM</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Entrada
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="text-yellow-600 font-bold">Baixo Saldo</span>
              </div>
              <div className="flex justify-between">
                <span>Saldo Atual:</span>
                <span className="font-bold text-orange-600">7.605.000 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Capacidade:</span>
                <span className="font-bold">45.000.000 Kz</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-green-600" />
              </div>
              <Button className="w-full mt-4" variant="outline">Carregar ATM</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              ATM Drive-Through
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>Status:</span>
                <span className="text-red-600 font-bold">Offline</span>
              </div>
              <div className="flex justify-between">
                <span>Saldo Atual:</span>
                <span className="font-bold">0 Kz</span>
              </div>
              <div className="flex justify-between">
                <span>Capacidade:</span>
                <span className="font-bold">67.500.000 Kz</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Conexão:</span>
                <Wifi className="h-4 w-4 text-red-600" />
              </div>
              <div className="flex justify-between items-center">
                <span>Energia:</span>
                <Battery className="h-4 w-4 text-red-600" />
              </div>
              <Button className="w-full mt-4" variant="destructive" disabled>
                Manutenção
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="dark:text-gray-100">Histórico de Carregamentos</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Principal - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Hoje, 09:30 por João Silva</div>
              </div>
              <div className="text-green-600 font-bold">+22.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Entrada - Carregamento</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Ontem, 16:45 por Maria Santos</div>
              </div>
              <div className="text-green-600 font-bold">+13.500.000 Kz</div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div>
                <div className="font-medium dark:text-gray-100">ATM Drive-Through - Manutenção</div>
                <div className="text-sm text-gray-500 dark:text-gray-400">2 dias atrás por Técnico Externo</div>
              </div>
              <div className="text-red-600 font-bold">Offline</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CarregamentoATM;
