import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { 
  Home, 
  Contact, 
  FileText, 
  CreditCard, 
  Image,
  AlertTriangle
} from 'lucide-react';

const AbrirContaParticular = () => {
  const [activeTab, setActiveTab] = useState('dados-identificativos');
  const { toast } = useToast();

  // Estados para os formulários
  const [dadosIdentificativos, setDadosIdentificativos] = useState({
    nome: '',
    dataNascimento: '',
    sexo: '',
    numeroIdentificacao: '',
    tipoDocumento: '', // BI, Passaporte, C.Residente
    localEmissao: '',
    dataEmissao: '',
    dataValidade: '',
    nif: '',
    nacionalidade: '',
    naturalidade: '',
    estadoCivil: '',
    separadoJudicialmente: false,
    regimeCasamento: '',
    exerceCargoPublico: '',
    qualCargoPublico: '',
    provincia: '',
    municipio: '',
    bairro: '',
    rua: ''
  });

  const [contactos, setContactos] = useState({
    telefonePersonal: '',
    emailPersonal: '',
    telefoneProfissional: '',
    emailProfissional: ''
  });

  const [habilitacao, setHabilitacao] = useState({
    semEstudos: false,
    ensinoPrimario: false,
    ensinoSecundario: false,
    ensinoMedio: false,
    cursoSuperior: false,
    estudante: false,
    reformado: false,
    domestico: false,
    desempregado: false,
    viuvaRendimento: false,
    trabalhadorContaOutrem: false,
    trabalhadorContaPropria: false,
    profissao: '',
    funcao: '',
    entidadePatronal: '',
    cidade: '',
    pais: '',
    rendimentoCliente: '',
    naturezaRendimento: ''
  });

  const [dadosConta, setDadosConta] = useState({
    natureza: '',
    tipoConta: ''
  });

  const [ficheiros, setFicheiros] = useState({
    imagemAssinatura: null,
    imagemBI: null,
    imagemPassaporte: null,
    imagemDeclaracaoServico: null,
    imagemPerfil: null
  });

  const handleTerminarRegistro = () => {
    toast({
      title: "Registro Concluído",
      description: "Conta particular criada com sucesso!",
    });
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Abertura de Conta Particular</h1>
        <p className="text-gray-600 dark:text-gray-400">Registar Cliente</p>
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger 
                value="dados-identificativos" 
                className="flex items-center space-x-2 text-xs"
              >
                <Home className="h-4 w-4" />
                <span className="hidden sm:inline">Dados Identificativos</span>
              </TabsTrigger>
              <TabsTrigger 
                value="contactos" 
                className="flex items-center space-x-2 text-xs"
              >
                <Contact className="h-4 w-4" />
                <span className="hidden sm:inline">Contactos</span>
              </TabsTrigger>
              <TabsTrigger 
                value="habilitacao" 
                className="flex items-center space-x-2 text-xs"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Habilitação e Dados Profissionais</span>
              </TabsTrigger>
              <TabsTrigger 
                value="dados-conta" 
                className="flex items-center space-x-2 text-xs"
              >
                <CreditCard className="h-4 w-4" />
                <span className="hidden sm:inline">Dados da Conta</span>
              </TabsTrigger>
              <TabsTrigger 
                value="ficheiros" 
                className="flex items-center space-x-2 text-xs"
              >
                <Image className="h-4 w-4" />
                <span className="hidden sm:inline">Ficheiros</span>
              </TabsTrigger>
              <TabsTrigger 
                value="validacao" 
                className="flex items-center space-x-2 text-xs bg-red-500 text-white"
              >
                <AlertTriangle className="h-4 w-4" />
                <span className="hidden sm:inline">Validação de Dados</span>
              </TabsTrigger>
            </TabsList>

            {/* Aba Contactos */}
            <TabsContent value="contactos" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Telefones Pessoais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-personal">Telefone Pessoal <span className="text-red-500">*</span></Label>
                    <Input
                      id="telefone-personal"
                      value={contactos.telefonePersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefonePersonal: e.target.value }))}
                      placeholder="Telefone pessoal"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-personal">Email Pessoal <span className="text-red-500">*</span></Label>
                    <Input
                      id="email-personal"
                      type="email"
                      value={contactos.emailPersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailPersonal: e.target.value }))}
                      placeholder="Email pessoal"
                    />
                  </div>
                </div>

                {/* Telefones Profissionais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-profissional">Telefone Profissional <span className="text-red-500">*</span></Label>
                    <Input
                      id="telefone-profissional"
                      value={contactos.telefoneProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefoneProfissional: e.target.value }))}
                      placeholder="Telefone profissional"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-profissional">Email Profissional <span className="text-red-500">*</span></Label>
                    <Input
                      id="email-profissional"
                      type="email"
                      value={contactos.emailProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailProfissional: e.target.value }))}
                      placeholder="Email profissional"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Dados da Conta */}
            <TabsContent value="dados-conta" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="natureza">Natureza <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.natureza} onValueChange={(value) => setDadosConta(prev => ({ ...prev, natureza: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona Natureza" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kwanza">Kwanza - AKZ</SelectItem>
                        <SelectItem value="euro">EURO - EUR</SelectItem>
                        <SelectItem value="dolar">US Dolar - USD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="tipo-conta">Tipo Conta <span className="text-red-500">*</span></Label>
                    <Select value={dadosConta.tipoConta} onValueChange={(value) => setDadosConta(prev => ({ ...prev, tipoConta: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona o Tipo de Conta" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="particular">Conta Particular/Singular</SelectItem>
                        <SelectItem value="salario">Conta Salário</SelectItem>
                        <SelectItem value="junior">Conta Júnior (2 titular)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Dados Identificativos */}
            <TabsContent value="dados-identificativos" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Primeira coluna */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome <span className="text-red-500">*</span></Label>
                    <Input
                      id="nome"
                      value={dadosIdentificativos.nome}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nome: e.target.value }))}
                      placeholder="Nome completo"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-nascimento">Data Nascimento <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-nascimento"
                      type="date"
                      value={dadosIdentificativos.dataNascimento}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataNascimento: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Sexo:</Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sexo-m"
                          checked={dadosIdentificativos.sexo === 'M'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, sexo: checked ? 'M' : '' }))}
                        />
                        <Label htmlFor="sexo-m">M</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="sexo-f"
                          checked={dadosIdentificativos.sexo === 'F'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, sexo: checked ? 'F' : '' }))}
                        />
                        <Label htmlFor="sexo-f">F</Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="numero-identificacao">Nº Identificação</Label>
                    <Input
                      id="numero-identificacao"
                      value={dadosIdentificativos.numeroIdentificacao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, numeroIdentificacao: e.target.value }))}
                      placeholder="Número de identificação"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Tipo de Documento:</Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="doc-bi"
                          checked={dadosIdentificativos.tipoDocumento === 'BI'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, tipoDocumento: checked ? 'BI' : '' }))}
                        />
                        <Label htmlFor="doc-bi">BI</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="doc-passaporte"
                          checked={dadosIdentificativos.tipoDocumento === 'Passaporte'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, tipoDocumento: checked ? 'Passaporte' : '' }))}
                        />
                        <Label htmlFor="doc-passaporte">Passaporte</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="doc-residente"
                          checked={dadosIdentificativos.tipoDocumento === 'C.Residente'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, tipoDocumento: checked ? 'C.Residente' : '' }))}
                        />
                        <Label htmlFor="doc-residente">C.Residente</Label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Segunda coluna */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="local-emissao">Local de Emissão <span className="text-red-500">*</span></Label>
                    <Input
                      id="local-emissao"
                      value={dadosIdentificativos.localEmissao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, localEmissao: e.target.value }))}
                      placeholder="Local de emissão"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-emissao">Data Emissão <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-emissao"
                      type="date"
                      value={dadosIdentificativos.dataEmissao}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataEmissao: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="data-validade">Data Validade <span className="text-red-500">*</span></Label>
                    <Input
                      id="data-validade"
                      type="date"
                      value={dadosIdentificativos.dataValidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, dataValidade: e.target.value }))}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nif">NIF <span className="text-red-500">*</span></Label>
                    <Input
                      id="nif"
                      value={dadosIdentificativos.nif}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nif: e.target.value }))}
                      placeholder="Número de identificação fiscal"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="nacionalidade">Nacionalidade <span className="text-red-500">*</span></Label>
                    <Input
                      id="nacionalidade"
                      value={dadosIdentificativos.nacionalidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, nacionalidade: e.target.value }))}
                      placeholder="Nacionalidade"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="naturalidade">Naturalidade <span className="text-red-500">*</span></Label>
                    <Input
                      id="naturalidade"
                      value={dadosIdentificativos.naturalidade}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, naturalidade: e.target.value }))}
                      placeholder="Naturalidade"
                    />
                  </div>
                </div>

                {/* Terceira coluna */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Estado Civil:</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {['Solteiro(a)', 'Casado(a)', 'União de Factos', 'Viúvo(a)', 'Divorciado(a)'].map((estado) => (
                        <div key={estado} className="flex items-center space-x-2">
                          <Checkbox
                            id={`estado-${estado}`}
                            checked={dadosIdentificativos.estadoCivil === estado}
                            onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, estadoCivil: checked ? estado : '' }))}
                          />
                          <Label htmlFor={`estado-${estado}`} className="text-sm">{estado}</Label>
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <Checkbox
                        id="separado-judicialmente"
                        checked={dadosIdentificativos.separadoJudicialmente}
                        onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, separadoJudicialmente: !!checked }))}
                      />
                      <Label htmlFor="separado-judicialmente" className="text-sm">Separado(a) Judicialmente</Label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Indicar Regime:</Label>
                    <div className="space-y-2">
                      {['Comunhão Adquiridos', 'Comunhão Geral', 'Separação de Bens', 'Outros regimes'].map((regime) => (
                        <div key={regime} className="flex items-center space-x-2">
                          <Checkbox
                            id={`regime-${regime}`}
                            checked={dadosIdentificativos.regimeCasamento === regime}
                            onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, regimeCasamento: checked ? regime : '' }))}
                          />
                          <Label htmlFor={`regime-${regime}`} className="text-sm">{regime}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Exercer um Cargo Público? <span className="text-red-500">*</span></Label>
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="cargo-sim"
                          checked={dadosIdentificativos.exerceCargoPublico === 'Sim'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, exerceCargoPublico: checked ? 'Sim' : '' }))}
                        />
                        <Label htmlFor="cargo-sim">Sim</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="cargo-nao"
                          checked={dadosIdentificativos.exerceCargoPublico === 'Não'}
                          onCheckedChange={(checked) => setDadosIdentificativos(prev => ({ ...prev, exerceCargoPublico: checked ? 'Não' : '' }))}
                        />
                        <Label htmlFor="cargo-nao">Não</Label>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="qual-cargo">Qual?</Label>
                    <Input
                      id="qual-cargo"
                      value={dadosIdentificativos.qualCargoPublico}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, qualCargoPublico: e.target.value }))}
                      placeholder="Especificar cargo público"
                      disabled={dadosIdentificativos.exerceCargoPublico !== 'Sim'}
                    />
                  </div>
                </div>
              </div>

              {/* Endereço */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">Endereço</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="provincia">Província <span className="text-red-500">*</span></Label>
                    <Input
                      id="provincia"
                      value={dadosIdentificativos.provincia}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, provincia: e.target.value }))}
                      placeholder="Província"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="municipio">Município <span className="text-red-500">*</span></Label>
                    <Input
                      id="municipio"
                      value={dadosIdentificativos.municipio}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, municipio: e.target.value }))}
                      placeholder="Município"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bairro">Bairro <span className="text-red-500">*</span></Label>
                    <Input
                      id="bairro"
                      value={dadosIdentificativos.bairro}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, bairro: e.target.value }))}
                      placeholder="Bairro"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rua">Rua <span className="text-red-500">*</span></Label>
                    <Input
                      id="rua"
                      value={dadosIdentificativos.rua}
                      onChange={(e) => setDadosIdentificativos(prev => ({ ...prev, rua: e.target.value }))}
                      placeholder="Rua"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="habilitacao" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Habilitações Literárias */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Habilitações Literárias</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="sem-estudos"
                        checked={habilitacao.semEstudos}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, semEstudos: !!checked }))}
                      />
                      <Label htmlFor="sem-estudos">S/Estudos</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="ensino-primario"
                        checked={habilitacao.ensinoPrimario}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, ensinoPrimario: !!checked }))}
                      />
                      <Label htmlFor="ensino-primario">Ensino Primário</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="ensino-secundario"
                        checked={habilitacao.ensinoSecundario}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, ensinoSecundario: !!checked }))}
                      />
                      <Label htmlFor="ensino-secundario">Ensino Secundário</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="ensino-medio"
                        checked={habilitacao.ensinoMedio}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, ensinoMedio: !!checked }))}
                      />
                      <Label htmlFor="ensino-medio">Ensino Médio</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="curso-superior"
                        checked={habilitacao.cursoSuperior}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, cursoSuperior: !!checked }))}
                      />
                      <Label htmlFor="curso-superior">Curso Superior</Label>
                    </div>
                  </div>

                  <h3 className="text-lg font-semibold mt-6">Atividade Profissional</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="estudante"
                        checked={habilitacao.estudante}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, estudante: !!checked }))}
                      />
                      <Label htmlFor="estudante">Estudante</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="reformado"
                        checked={habilitacao.reformado}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, reformado: !!checked }))}
                      />
                      <Label htmlFor="reformado">Reformado(a)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="domestico"
                        checked={habilitacao.domestico}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, domestico: !!checked }))}
                      />
                      <Label htmlFor="domestico">Doméstico(a)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="desempregado"
                        checked={habilitacao.desempregado}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, desempregado: !!checked }))}
                      />
                      <Label htmlFor="desempregado">Desempregado(a)</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="viuva-rendimento"
                        checked={habilitacao.viuvaRendimento}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, viuvaRendimento: !!checked }))}
                      />
                      <Label htmlFor="viuva-rendimento">Viúva de rendimento</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="trabalhador-conta-outrem"
                        checked={habilitacao.trabalhadorContaOutrem}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, trabalhadorContaOutrem: !!checked }))}
                      />
                      <Label htmlFor="trabalhador-conta-outrem">Trabalhador(a) Por Conta de Outrem</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="trabalhador-conta-propria"
                        checked={habilitacao.trabalhadorContaPropria}
                        onCheckedChange={(checked) => setHabilitacao(prev => ({ ...prev, trabalhadorContaPropria: !!checked }))}
                      />
                      <Label htmlFor="trabalhador-conta-propria">Trabalhador(a) Por Conta de própria</Label>
                    </div>
                  </div>
                </div>

                {/* Dados Profissionais */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Dados Profissionais</h3>

                  <div className="space-y-2">
                    <Label htmlFor="profissao">Profissão <span className="text-red-500">*</span></Label>
                    <Input
                      id="profissao"
                      value={habilitacao.profissao}
                      onChange={(e) => setHabilitacao(prev => ({ ...prev, profissao: e.target.value }))}
                      placeholder="Profissão"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="funcao">Função <span className="text-red-500">*</span></Label>
                    <Input
                      id="funcao"
                      value={habilitacao.funcao}
                      onChange={(e) => setHabilitacao(prev => ({ ...prev, funcao: e.target.value }))}
                      placeholder="Função"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="entidade-patronal">Entidade Patronal / Morada Sede <span className="text-red-500">*</span></Label>
                    <Input
                      id="entidade-patronal"
                      value={habilitacao.entidadePatronal}
                      onChange={(e) => setHabilitacao(prev => ({ ...prev, entidadePatronal: e.target.value }))}
                      placeholder="Entidade patronal"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="cidade">Cidade <span className="text-red-500">*</span></Label>
                      <Input
                        id="cidade"
                        value={habilitacao.cidade}
                        onChange={(e) => setHabilitacao(prev => ({ ...prev, cidade: e.target.value }))}
                        placeholder="Cidade"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pais">País <span className="text-red-500">*</span></Label>
                      <Input
                        id="pais"
                        value={habilitacao.pais}
                        onChange={(e) => setHabilitacao(prev => ({ ...prev, pais: e.target.value }))}
                        placeholder="País"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="rendimento-cliente">Rendimento do Cliente <span className="text-red-500">*</span></Label>
                    <Input
                      id="rendimento-cliente"
                      value={habilitacao.rendimentoCliente}
                      onChange={(e) => setHabilitacao(prev => ({ ...prev, rendimentoCliente: e.target.value }))}
                      placeholder="Rendimento mensal"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="natureza-rendimentos">Natureza do Rendimentos <span className="text-red-500">*</span></Label>
                    <Select value={habilitacao.naturezaRendimento} onValueChange={(value) => setHabilitacao(prev => ({ ...prev, naturezaRendimento: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona Natureza" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="salario">Salário</SelectItem>
                        <SelectItem value="pensao">Pensão</SelectItem>
                        <SelectItem value="negocio">Negócio Próprio</SelectItem>
                        <SelectItem value="investimentos">Investimentos</SelectItem>
                        <SelectItem value="outros">Outros</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="ficheiros" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Imagem Assinatura */}
                <div className="space-y-4">
                  <Label htmlFor="imagem-assinatura">Imagem Assinatura <span className="text-red-500">*</span></Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Escolher ficheiro
                      </Button>
                      <p className="text-sm text-gray-500">Nenhum ficheiro selecionado</p>
                    </div>
                  </div>
                </div>

                {/* Imagem BI */}
                <div className="space-y-4">
                  <Label htmlFor="imagem-bi">Imagem BI <span className="text-red-500">*</span></Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Escolher ficheiro
                      </Button>
                      <p className="text-sm text-gray-500">Nenhum ficheiro selecionado</p>
                    </div>
                  </div>
                </div>

                {/* Imagem Passaporte */}
                <div className="space-y-4">
                  <Label htmlFor="imagem-passaporte">Imagem Passaporte <span className="text-red-500">*</span></Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Escolher ficheiro
                      </Button>
                      <p className="text-sm text-gray-500">Nenhum ficheiro selecionado</p>
                    </div>
                  </div>
                </div>

                {/* Imagem Declaração Serviço */}
                <div className="space-y-4">
                  <Label htmlFor="imagem-declaracao">Imagem Declaração Serviço <span className="text-red-500">*</span></Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Escolher ficheiro
                      </Button>
                      <p className="text-sm text-gray-500">Nenhum ficheiro selecionado</p>
                    </div>
                  </div>
                </div>

                {/* Imagem Perfil */}
                <div className="space-y-4">
                  <Label htmlFor="imagem-perfil">Imagem Perfil <span className="text-red-500">*</span></Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full">
                        Escolher ficheiro
                      </Button>
                      <p className="text-sm text-gray-500">Nenhum ficheiro selecionado</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Botão Terminar Registo */}
              <div className="flex justify-center pt-6">
                <Button
                  onClick={handleTerminarRegistro}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2"
                >
                  Terminar Registo
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="validacao" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Validação de Dados - Implementar validação final</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AbrirContaParticular;
