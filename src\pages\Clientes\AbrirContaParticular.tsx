import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { 
  Home, 
  Contact, 
  FileText, 
  CreditCard, 
  Image,
  AlertTriangle
} from 'lucide-react';

const AbrirContaParticular = () => {
  const [activeTab, setActiveTab] = useState('dados-identificativos');
  const { toast } = useToast();

  // Estados para os formulários
  const [contactos, setContactos] = useState({
    telefonePersonal: '',
    telefonePersonal2: '',
    emailPersonal: '',
    telefoneProfissional: '',
    telefoneProfissional2: '',
    emailProfissional: ''
  });

  const [habilitacao, setHabilitacao] = useState({
    semEstudos: false,
    ensinoPrimario: false,
    ensinoSecundario: false,
    ensinoMedio: false,
    cursoSuperior: false,
    estudante: false,
    reformado: false,
    domestico: false,
    trabalhadorContaOutrem: false,
    trabalhadorContaPropria: false,
    profissao: '',
    funcao: '',
    entidadePatronal: '',
    cidade: '',
    pais: '',
    rendimentoCliente: '',
    naturezaRendimento: ''
  });

  const [dadosConta, setDadosConta] = useState({
    natureza: '',
    tipoConta: ''
  });

  const handleTerminarRegistro = () => {
    toast({
      title: "Registro Concluído",
      description: "Conta particular criada com sucesso!",
    });
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Abertura de Conta Particular</h1>
        <p className="text-gray-600 dark:text-gray-400">Registar Cliente</p>
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger 
                value="dados-identificativos" 
                className="flex items-center space-x-2 text-xs"
              >
                <Home className="h-4 w-4" />
                <span className="hidden sm:inline">Dados Identificativos</span>
              </TabsTrigger>
              <TabsTrigger 
                value="contactos" 
                className="flex items-center space-x-2 text-xs"
              >
                <Contact className="h-4 w-4" />
                <span className="hidden sm:inline">Contactos</span>
              </TabsTrigger>
              <TabsTrigger 
                value="habilitacao" 
                className="flex items-center space-x-2 text-xs"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Habilitação e Dados Profissionais</span>
              </TabsTrigger>
              <TabsTrigger 
                value="dados-conta" 
                className="flex items-center space-x-2 text-xs"
              >
                <CreditCard className="h-4 w-4" />
                <span className="hidden sm:inline">Dados da Conta</span>
              </TabsTrigger>
              <TabsTrigger 
                value="ficheiros" 
                className="flex items-center space-x-2 text-xs"
              >
                <Image className="h-4 w-4" />
                <span className="hidden sm:inline">Ficheiros</span>
              </TabsTrigger>
              <TabsTrigger 
                value="validacao" 
                className="flex items-center space-x-2 text-xs bg-red-500 text-white"
              >
                <AlertTriangle className="h-4 w-4" />
                <span className="hidden sm:inline">Validação de Dados</span>
              </TabsTrigger>
            </TabsList>

            {/* Aba Contactos */}
            <TabsContent value="contactos" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Telefones Pessoais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-personal">Telefone Pessoal *</Label>
                    <Input
                      id="telefone-personal"
                      value={contactos.telefonePersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefonePersonal: e.target.value }))}
                      placeholder="Telefone pessoal"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="telefone-personal-2">Telefone Pessoal 2 *</Label>
                    <Input
                      id="telefone-personal-2"
                      value={contactos.telefonePersonal2}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefonePersonal2: e.target.value }))}
                      placeholder="Telefone pessoal 2"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-personal">Email Pessoal *</Label>
                    <Input
                      id="email-personal"
                      type="email"
                      value={contactos.emailPersonal}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailPersonal: e.target.value }))}
                      placeholder="Email pessoal"
                    />
                  </div>
                </div>

                {/* Telefones Profissionais */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="telefone-profissional">Telefone Profissional *</Label>
                    <Input
                      id="telefone-profissional"
                      value={contactos.telefoneProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefoneProfissional: e.target.value }))}
                      placeholder="Telefone profissional"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="telefone-profissional-2">Telefone Profissional 2*</Label>
                    <Input
                      id="telefone-profissional-2"
                      value={contactos.telefoneProfissional2}
                      onChange={(e) => setContactos(prev => ({ ...prev, telefoneProfissional2: e.target.value }))}
                      placeholder="Telefone profissional 2"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email-profissional">Email Profissional*</Label>
                    <Input
                      id="email-profissional"
                      type="email"
                      value={contactos.emailProfissional}
                      onChange={(e) => setContactos(prev => ({ ...prev, emailProfissional: e.target.value }))}
                      placeholder="Email profissional"
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Aba Dados da Conta */}
            <TabsContent value="dados-conta" className="mt-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="natureza">Natureza*</Label>
                    <Select value={dadosConta.natureza} onValueChange={(value) => setDadosConta(prev => ({ ...prev, natureza: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona Natureza" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kwanza">Kwanza - AKZ</SelectItem>
                        <SelectItem value="euro">EURO - EUR</SelectItem>
                        <SelectItem value="dolar">US Dolar - USD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="tipo-conta">Tipo Conta *</Label>
                    <Select value={dadosConta.tipoConta} onValueChange={(value) => setDadosConta(prev => ({ ...prev, tipoConta: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleciona o Tipo de Conta" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="poupanca">Conta Poupança</SelectItem>
                        <SelectItem value="ordem">Ordem Doméstica</SelectItem>
                        <SelectItem value="ordenado">Ordem Ordenado/Salário</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Outras abas */}
            <TabsContent value="dados-identificativos" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Dados Identificativos - Implementar formulário completo</p>
              </div>
            </TabsContent>

            <TabsContent value="habilitacao" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Habilitação e Dados Profissionais - Implementar formulário completo</p>
              </div>
            </TabsContent>

            <TabsContent value="ficheiros" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Ficheiros - Implementar upload de documentos</p>
              </div>
            </TabsContent>

            <TabsContent value="validacao" className="mt-6">
              <div className="text-center py-8 text-gray-500">
                <p>Aba Validação de Dados - Implementar validação final</p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default AbrirContaParticular;
