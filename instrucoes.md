
# twins_bank - Instruções de Desenvolvimento

## Stack Utilizada
- **React 18** com TypeScript
- **Tailwind CSS** para estilização
- **Shadcn/ui** para componentes de interface
- **Lucide React** para ícones
- **React Router DOM** para navegação
- **Recharts** para gráficos e dashboards

## Diretrizes Técnicas e de Arquitetura

### Componentização
- Componentes funcionais com hooks
- Separação clara entre componentes de layout e páginas
- Utilização de tipos TypeScript para props
- Componentes reutilizáveis na pasta `src/components`
- Páginas específicas na pasta `src/pages`

### Controle por Variáveis de Ambiente
- Configurações de API em `.env`
- URLs de endpoints configuráveis
- Chaves de autenticação via environment variables
- Modo de desenvolvimento/produção

### Estrutura Modular
```
src/
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Layout.tsx
│   └── ui/ (shadcn components)
├── pages/
│   ├── Dashboard.tsx
│   ├── Clientes/
│   ├── Transferencias/
│   └── Sistema/
├── hooks/
├── utils/
└── types/
```

## Etapas do Desenvolvimento

### Fase 1: Configuração e Layout Base
1. Configuração do projeto React + TypeScript
2. Instalação e configuração do Tailwind CSS
3. Setup do Shadcn/ui com tema personalizado
4. Criação dos componentes de layout (Header, Sidebar, Layout)
5. Implementação do dashboard principal

### Fase 2: Funcionalidades Core
1. Sistema de navegação e roteamento
2. Gestão de clientes (CRUD)
3. Sistema de transferências internas
4. Dashboard com métricas em tempo real

### Fase 3: Funcionalidades Avançadas
1. Sistema de cartões
2. Operações de câmbio
3. Integração com ATM
4. Relatórios e analytics

## Padrões de Código
- Nomenclatura em português para componentes de negócio
- Utilização consistente de TypeScript
- Componentes com responsabilidade única
- Hooks customizados para lógica complexa
