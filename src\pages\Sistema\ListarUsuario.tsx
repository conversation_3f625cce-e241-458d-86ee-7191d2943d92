import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Users, Search, Edit, Trash2, UserCheck, UserX, Eye } from 'lucide-react';
import ActionMenu, { ActionMenuItem } from '@/components/ui/ActionMenu';

interface Usuario {
  id: number;
  nome: string;
  email: string;
  perfil: string;
  balcao: string;
  status: 'ativo' | 'inativo' | 'bloqueado';
  ultimoAcesso: string;
  telefone?: string;
}

const ListarUsuario = () => {
  const [usuarios] = useState<Usuario[]>([
    {
      id: 1,
      nome: '<PERSON>',
      email: '<EMAIL>',
      perfil: 'Administrador',
      balcao: 'Sede Principal',
      status: 'ativo',
      ultimoAcesso: '2025-06-16 14:30',
      telefone: '+244 900 123 456'
    },
    {
      id: 2,
      nome: 'Maria Fernanda Costa',
      email: '<EMAIL>',
      perfil: 'Gerente',
      balcao: 'Luanda Centro',
      status: 'ativo',
      ultimoAcesso: '2025-06-16 13:45',
      telefone: '+244 900 234 567'
    },
    {
      id: 3,
      nome: 'António José Pereira',
      email: '<EMAIL>',
      perfil: 'Operador de Caixa',
      balcao: 'Luanda Sul',
      status: 'inativo',
      ultimoAcesso: '2025-06-15 16:20',
      telefone: '+244 900 345 678'
    },
    {
      id: 4,
      nome: 'Ana Paula Santos',
      email: '<EMAIL>',
      perfil: 'Tesoureiro',
      balcao: 'Benguela',
      status: 'ativo',
      ultimoAcesso: '2025-06-16 15:10',
      telefone: '+244 900 456 789'
    },
    {
      id: 5,
      nome: 'Carlos Eduardo Lima',
      email: '<EMAIL>',
      perfil: 'Atendimento ao Cliente',
      balcao: 'Huambo',
      status: 'bloqueado',
      ultimoAcesso: '2025-06-14 10:30',
      telefone: '+244 900 567 890'
    }
  ]);

  const [filtros, setFiltros] = useState({
    search: '',
    perfil: 'all',
    status: 'all',
    balcao: 'all',
    itemsPorPagina: '10'
  });

  const { toast } = useToast();

  const perfis = [
    { value: 'all', label: 'Todos os Perfis' },
    { value: 'Administrador', label: 'Administrador' },
    { value: 'Gerente', label: 'Gerente' },
    { value: 'Operador de Caixa', label: 'Operador de Caixa' },
    { value: 'Tesoureiro', label: 'Tesoureiro' },
    { value: 'Atendimento ao Cliente', label: 'Atendimento ao Cliente' }
  ];

  const balcoes = [
    { value: 'all', label: 'Todos os Balcões' },
    { value: 'Sede Principal', label: 'Sede Principal' },
    { value: 'Luanda Centro', label: 'Luanda Centro' },
    { value: 'Luanda Sul', label: 'Luanda Sul' },
    { value: 'Benguela', label: 'Benguela' },
    { value: 'Huambo', label: 'Huambo' }
  ];

  const usuariosFiltrados = usuarios.filter(usuario => {
    const matchSearch = !filtros.search ||
      usuario.nome.toLowerCase().includes(filtros.search.toLowerCase()) ||
      usuario.email.toLowerCase().includes(filtros.search.toLowerCase());

    const matchPerfil = filtros.perfil === 'all' || usuario.perfil === filtros.perfil;
    const matchStatus = filtros.status === 'all' || usuario.status === filtros.status;
    const matchBalcao = filtros.balcao === 'all' || usuario.balcao === filtros.balcao;

    return matchSearch && matchPerfil && matchStatus && matchBalcao;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ativo':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Ativo</Badge>;
      case 'inativo':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Inativo</Badge>;
      case 'bloqueado':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Bloqueado</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleAtivarUsuario = (id: number) => {
    toast({
      title: "Usuário ativado",
      description: "O usuário foi ativado com sucesso",
    });
  };

  const handleDesativarUsuario = (id: number) => {
    toast({
      title: "Usuário desativado",
      description: "O usuário foi desativado com sucesso",
      variant: "destructive"
    });
  };

  const handleEditarUsuario = (id: number) => {
    toast({
      title: "Editar usuário",
      description: "Funcionalidade de edição será implementada",
    });
  };

  const handleExcluirUsuario = (id: number) => {
    toast({
      title: "Excluir usuário",
      description: "Funcionalidade de exclusão será implementada",
      variant: "destructive"
    });
  };

  const handleVisualizarUsuario = (id: number) => {
    toast({
      title: "Visualizar usuário",
      description: "Funcionalidade de visualização será implementada",
    });
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Users className="h-8 w-8" />
          Listar Usuários
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">Gerenciar usuários do sistema</p>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filtros de Pesquisa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Pesquisar</label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Nome ou email..."
                  value={filtros.search}
                  onChange={(e) => setFiltros(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Perfil</label>
              <Select value={filtros.perfil} onValueChange={(value) => setFiltros(prev => ({ ...prev, perfil: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {perfis.map((perfil) => (
                    <SelectItem key={perfil.value} value={perfil.value}>
                      {perfil.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={filtros.status} onValueChange={(value) => setFiltros(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os Status</SelectItem>
                  <SelectItem value="ativo">Ativo</SelectItem>
                  <SelectItem value="inativo">Inativo</SelectItem>
                  <SelectItem value="bloqueado">Bloqueado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Balcão</label>
              <Select value={filtros.balcao} onValueChange={(value) => setFiltros(prev => ({ ...prev, balcao: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {balcoes.map((balcao) => (
                    <SelectItem key={balcao.value} value={balcao.value}>
                      {balcao.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Itens por página</label>
              <Select value={filtros.itemsPorPagina} onValueChange={(value) => setFiltros(prev => ({ ...prev, itemsPorPagina: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabela de Usuários */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            Usuários Encontrados ({usuariosFiltrados.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="table-container rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Perfil</TableHead>
                  <TableHead>Balcão</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Último Acesso</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usuariosFiltrados.slice(0, parseInt(filtros.itemsPorPagina)).map((usuario) => (
                  <TableRow key={usuario.id}>
                    <TableCell className="font-medium">{usuario.nome}</TableCell>
                    <TableCell>{usuario.email}</TableCell>
                    <TableCell>{usuario.perfil}</TableCell>
                    <TableCell>{usuario.balcao}</TableCell>
                    <TableCell>{getStatusBadge(usuario.status)}</TableCell>
                    <TableCell>{new Date(usuario.ultimoAcesso).toLocaleString('pt-AO')}</TableCell>
                    <TableCell>
                      <ActionMenu
                        items={[
                          {
                            label: 'Visualizar',
                            icon: Eye,
                            onClick: () => handleVisualizarUsuario(usuario.id)
                          },
                          {
                            label: 'Editar',
                            icon: Edit,
                            onClick: () => handleEditarUsuario(usuario.id),
                            separator: true
                          },
                          {
                            label: usuario.status === 'ativo' ? 'Desativar' : 'Ativar',
                            icon: usuario.status === 'ativo' ? UserX : UserCheck,
                            onClick: () => usuario.status === 'ativo'
                              ? handleDesativarUsuario(usuario.id)
                              : handleAtivarUsuario(usuario.id),
                            variant: usuario.status === 'ativo' ? 'warning' : 'default'
                          },
                          {
                            label: 'Excluir',
                            icon: Trash2,
                            onClick: () => handleExcluirUsuario(usuario.id),
                            variant: 'destructive'
                          }
                        ]}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ListarUsuario;
