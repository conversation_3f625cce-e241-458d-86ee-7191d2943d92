import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formatar valor monetário em Kwanza
 * @param value - Valor numérico
 * @param showSymbol - Se deve mostrar o símbolo Kz (padrão: true)
 * @returns String formatada com separador de milhares e duas casas decimais
 */
export function formatKwanza(value: number, showSymbol: boolean = true): string {
  const formatted = new Intl.NumberFormat('pt-AO', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);

  return showSymbol ? `${formatted} Kz` : formatted;
}

/**
 * Converter moeda estrangeira para Kwanza usando taxa de câmbio
 * @param foreignValue - Valor em moeda estrangeira
 * @param exchangeRate - Taxa de câmbio para Kwanza
 * @returns Valor convertido em kwanzas
 */
export function foreignToKwanza(foreignValue: number, exchangeRate: number): number {
  return foreignValue * exchangeRate;
}
