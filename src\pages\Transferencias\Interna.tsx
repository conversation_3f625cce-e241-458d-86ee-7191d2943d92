
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { CheckCircle, AlertCircle, Loader2, User, Building2, CreditCard } from 'lucide-react';

interface ContaInfo {
  numero: string;
  titular: string;
  tipo: string;
  saldo?: number;
  status: 'ativa' | 'inativa' | 'bloqueada';
}

interface FormErrors {
  natureza?: string;
  contaOrigem?: string;
  contaDestino?: string;
  valor?: string;
}

const TransferenciaInterna = () => {
  const [natureza, setNatureza] = useState('');
  const [contaOrigem, setContaOrigem] = useState('');
  const [contaDestino, setContaDestino] = useState('');
  const [valor, setValor] = useState('');
  const [descricao, setDescricao] = useState('');

  // Estados para verificação de contas
  const [contaOrigemInfo, setContaOrigemInfo] = useState<ContaInfo | null>(null);
  const [contaDestinoInfo, setContaDestinoInfo] = useState<ContaInfo | null>(null);
  const [verificandoOrigem, setVerificandoOrigem] = useState(false);
  const [verificandoDestino, setVerificandoDestino] = useState(false);

  // Estados para validação
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();

  const naturezas = [
    { value: 'kwanza-aoa', label: 'Kwanza - AOA' },
    { value: 'us-dollar-usd', label: 'US Dollar - USD' },
    { value: 'gbp-pound', label: 'Libra Esterlina - GBP' }
  ];

  // Função para formatar valor monetário
  const formatarValor = (value: string) => {
    const numericValue = value.replace(/[^\d]/g, '');
    if (!numericValue) return '';

    const number = parseInt(numericValue);
    return new Intl.NumberFormat('pt-AO').format(number);
  };

  // Função para validar número de conta
  const validarNumeroConta = (numero: string): boolean => {
    return /^\d{10,16}$/.test(numero.replace(/\s/g, ''));
  };

  // Simulação de verificação de conta
  const verificarConta = async (numeroConta: string): Promise<ContaInfo | null> => {
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simula delay da API

    if (!validarNumeroConta(numeroConta)) {
      throw new Error('Número de conta inválido');
    }

    // Simulação de dados de conta
    const contas: Record<string, ContaInfo> = {
      '1234567890': {
        numero: '1234567890',
        titular: 'João Manuel Silva',
        tipo: 'Conta Corrente',
        saldo: 450000,
        status: 'ativa'
      },
      '9876543210': {
        numero: '9876543210',
        titular: 'Maria Fernanda Costa',
        tipo: 'Conta Poupança',
        saldo: 125000,
        status: 'ativa'
      },
      '5555555555': {
        numero: '5555555555',
        titular: 'António José Pereira',
        tipo: 'Conta Corrente',
        saldo: 0,
        status: 'bloqueada'
      }
    };

    const conta = contas[numeroConta.replace(/\s/g, '')];
    if (!conta) {
      throw new Error('Conta não encontrada');
    }

    return conta;
  };

  const handleVerificarOrigem = async () => {
    if (!contaOrigem.trim()) {
      setErrors(prev => ({ ...prev, contaOrigem: 'Número da conta origem é obrigatório' }));
      return;
    }

    setVerificandoOrigem(true);
    setContaOrigemInfo(null);
    setErrors(prev => ({ ...prev, contaOrigem: undefined }));

    try {
      const info = await verificarConta(contaOrigem);
      setContaOrigemInfo(info);

      if (info?.status !== 'ativa') {
        toast({
          title: "Atenção",
          description: `Conta ${info?.status === 'bloqueada' ? 'bloqueada' : 'inativa'}. Verifique com o gerente.`,
          variant: "destructive"
        });
      } else {
        toast({
          title: "Conta verificada",
          description: "Conta origem verificada com sucesso",
        });
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, contaOrigem: (error as Error).message }));
      toast({
        title: "Erro na verificação",
        description: (error as Error).message,
        variant: "destructive"
      });
    } finally {
      setVerificandoOrigem(false);
    }
  };

  const handleVerificarDestino = async () => {
    if (!contaDestino.trim()) {
      setErrors(prev => ({ ...prev, contaDestino: 'Número da conta destino é obrigatório' }));
      return;
    }

    if (contaDestino === contaOrigem) {
      setErrors(prev => ({ ...prev, contaDestino: 'Conta destino não pode ser igual à conta origem' }));
      return;
    }

    setVerificandoDestino(true);
    setContaDestinoInfo(null);
    setErrors(prev => ({ ...prev, contaDestino: undefined }));

    try {
      const info = await verificarConta(contaDestino);
      setContaDestinoInfo(info);

      if (info?.status !== 'ativa') {
        toast({
          title: "Atenção",
          description: `Conta ${info?.status === 'bloqueada' ? 'bloqueada' : 'inativa'}. Transferência pode não ser processada.`,
          variant: "destructive"
        });
      } else {
        toast({
          title: "Conta verificada",
          description: "Conta destino verificada com sucesso",
        });
      }
    } catch (error) {
      setErrors(prev => ({ ...prev, contaDestino: (error as Error).message }));
      toast({
        title: "Erro na verificação",
        description: (error as Error).message,
        variant: "destructive"
      });
    } finally {
      setVerificandoDestino(false);
    }
  };

  // Validação do formulário
  const validarFormulario = (): boolean => {
    const newErrors: FormErrors = {};

    if (!natureza) {
      newErrors.natureza = 'Selecione a natureza da transferência';
    }

    if (!contaOrigem.trim()) {
      newErrors.contaOrigem = 'Número da conta origem é obrigatório';
    } else if (!validarNumeroConta(contaOrigem)) {
      newErrors.contaOrigem = 'Número de conta inválido (deve ter entre 10-16 dígitos)';
    }

    if (!contaDestino.trim()) {
      newErrors.contaDestino = 'Número da conta destino é obrigatório';
    } else if (!validarNumeroConta(contaDestino)) {
      newErrors.contaDestino = 'Número de conta inválido (deve ter entre 10-16 dígitos)';
    } else if (contaDestino === contaOrigem) {
      newErrors.contaDestino = 'Conta destino não pode ser igual à conta origem';
    }

    if (!valor.trim()) {
      newErrors.valor = 'Valor da transferência é obrigatório';
    } else {
      const valorNumerico = parseFloat(valor.replace(/[^\d,]/g, '').replace(',', '.'));
      if (valorNumerico <= 0) {
        newErrors.valor = 'Valor deve ser maior que zero';
      } else if (valorNumerico > 10000000) { // Limite de 10M Kz
        newErrors.valor = 'Valor excede o limite máximo de 10.000.000 Kz';
      } else if (contaOrigemInfo && contaOrigemInfo.saldo && valorNumerico > contaOrigemInfo.saldo) {
        newErrors.valor = 'Saldo insuficiente na conta origem';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleConfirmar = async () => {
    if (!validarFormulario()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    if (!contaOrigemInfo || !contaDestinoInfo) {
      toast({
        title: "Verificação necessária",
        description: "Verifique ambas as contas antes de confirmar a transferência",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simula processamento da transferência
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: "Transferência realizada",
        description: `Transferência de ${formatarValor(valor)} Kz realizada com sucesso`,
      });

      // Limpar formulário após sucesso
      setNatureza('');
      setContaOrigem('');
      setContaDestino('');
      setValor('');
      setDescricao('');
      setContaOrigemInfo(null);
      setContaDestinoInfo(null);
      setErrors({});

    } catch (error) {
      toast({
        title: "Erro na transferência",
        description: "Ocorreu um erro ao processar a transferência. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handlers para inputs com formatação
  const handleContaOrigemChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatarNumeroConta(e.target.value);
    setContaOrigem(formatted);
    setContaOrigemInfo(null); // Reset info quando número muda
    if (errors.contaOrigem) {
      setErrors(prev => ({ ...prev, contaOrigem: undefined }));
    }
  };

  const handleContaDestinoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatarNumeroConta(e.target.value);
    setContaDestino(formatted);
    setContaDestinoInfo(null); // Reset info quando número muda
    if (errors.contaDestino) {
      setErrors(prev => ({ ...prev, contaDestino: undefined }));
    }
  };

  const handleValorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatarValor(e.target.value);
    setValor(formatted);
    if (errors.valor) {
      setErrors(prev => ({ ...prev, valor: undefined }));
    }
  };

  // Verificar se o formulário está pronto para submissão
  const isFormValid = natureza && contaOrigemInfo && contaDestinoInfo && valor && !Object.keys(errors).length;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Transferencia Interna</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Dados da Transferência
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          {/* Natureza */}
          <div className="space-y-2">
            <Label htmlFor="natureza" className="text-sm font-medium">
              Natureza <span className="text-red-500">*</span>
            </Label>
            <Select value={natureza} onValueChange={(value) => {
              setNatureza(value);
              if (errors.natureza) {
                setErrors(prev => ({ ...prev, natureza: undefined }));
              }
            }}>
              <SelectTrigger className={`w-full ${errors.natureza ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Seleciona Natureza" />
              </SelectTrigger>
              <SelectContent>
                {naturezas.map((nat) => (
                  <SelectItem key={nat.value} value={nat.value}>
                    {nat.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.natureza && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.natureza}
              </p>
            )}
          </div>

          {/* Número Conta Origem */}
          <div className="space-y-2">
            <Label htmlFor="conta-origem" className="text-sm font-medium flex items-center dark:text-gray-100">
              <Building2 className="h-4 w-4 mr-1 text-blue-500" />
              Número Conta Origem <span className="text-red-500">*</span>
            </Label>
            <div className="flex gap-2">
              <Input
                id="conta-origem"
                value={contaOrigem}
                onChange={handleContaOrigemChange}
                placeholder="0000 0000 0000 0000"
                className={`flex-1 ${errors.contaOrigem ? 'border-red-500' : contaOrigemInfo ? 'border-green-500' : ''}`}
                maxLength={19} // 16 dígitos + 3 espaços
              />
              <Button
                onClick={handleVerificarOrigem}
                disabled={verificandoOrigem || !contaOrigem.trim()}
                className="bg-primary hover:bg-primary/90 text-white px-6 min-w-[120px] dark:bg-twins-primary dark:hover:bg-twins-secondary"
              >
                {verificandoOrigem ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Verificando...
                  </>
                ) : (
                  'Verificar'
                )}
              </Button>
            </div>
            {errors.contaOrigem && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.contaOrigem}
              </p>
            )}
            <div className={`p-3 rounded border min-h-[60px] ${contaOrigemInfo ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700' : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'}`}>
              {contaOrigemInfo ? (
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800 dark:text-green-300">Conta Verificada</span>
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    <p><strong>Titular:</strong> {contaOrigemInfo.titular}</p>
                    <p><strong>Tipo:</strong> {contaOrigemInfo.tipo}</p>
                    {contaOrigemInfo.saldo !== undefined && (
                      <p><strong>Saldo:</strong> {new Intl.NumberFormat('pt-AO').format(contaOrigemInfo.saldo)} Kz</p>
                    )}
                    <p><strong>Status:</strong>
                      <span className={`ml-1 px-2 py-1 rounded text-xs ${
                        contaOrigemInfo.status === 'ativa' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                        contaOrigemInfo.status === 'bloqueada' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' :
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                      }`}>
                        {contaOrigemInfo.status.toUpperCase()}
                      </span>
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-sm">Clique em "Verificar" para validar a conta origem</p>
              )}
            </div>
          </div>

          {/* Número Conta Destinatário */}
          <div className="space-y-2">
            <Label htmlFor="conta-destino" className="text-sm font-medium flex items-center dark:text-gray-100">
              <User className="h-4 w-4 mr-1 text-blue-500" />
              Número Conta Destinatário <span className="text-red-500">*</span>
            </Label>
            <div className="flex gap-2">
              <Input
                id="conta-destino"
                value={contaDestino}
                onChange={handleContaDestinoChange}
                placeholder="0000 0000 0000 0000"
                className={`flex-1 ${errors.contaDestino ? 'border-red-500' : contaDestinoInfo ? 'border-green-500' : ''}`}
                maxLength={19} // 16 dígitos + 3 espaços
              />
              <Button
                onClick={handleVerificarDestino}
                disabled={verificandoDestino || !contaDestino.trim()}
                className="bg-primary hover:bg-primary/90 text-white px-6 min-w-[120px] dark:bg-twins-primary dark:hover:bg-twins-secondary"
              >
                {verificandoDestino ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Verificando...
                  </>
                ) : (
                  'Verificar'
                )}
              </Button>
            </div>
            {errors.contaDestino && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.contaDestino}
              </p>
            )}
            <div className={`p-3 rounded border min-h-[60px] ${contaDestinoInfo ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700' : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600'}`}>
              {contaDestinoInfo ? (
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-800 dark:text-green-300">Conta Verificada</span>
                  </div>
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    <p><strong>Titular:</strong> {contaDestinoInfo.titular}</p>
                    <p><strong>Tipo:</strong> {contaDestinoInfo.tipo}</p>
                    <p><strong>Status:</strong>
                      <span className={`ml-1 px-2 py-1 rounded text-xs ${
                        contaDestinoInfo.status === 'ativa' ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' :
                        contaDestinoInfo.status === 'bloqueada' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' :
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                      }`}>
                        {contaDestinoInfo.status.toUpperCase()}
                      </span>
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-sm">Clique em "Verificar" para validar a conta destinatário</p>
              )}
            </div>
          </div>

          {/* Valor */}
          <div className="space-y-2">
            <Label htmlFor="valor" className="text-sm font-medium">
              Valor da Transferência <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="valor"
                value={valor}
                onChange={handleValorChange}
                placeholder="0"
                className={`pr-12 ${errors.valor ? 'border-red-500' : ''}`}
                type="text"
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                Kz
              </span>
            </div>
            {errors.valor && (
              <p className="text-sm text-red-500 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.valor}
              </p>
            )}
            <p className="text-xs text-gray-500">
              Limite máximo: 10.000.000 Kz por transferência
            </p>
          </div>

          {/* Descrição (Opcional) */}
          <div className="space-y-2">
            <Label htmlFor="descricao" className="text-sm font-medium">
              Descrição (Opcional)
            </Label>
            <Input
              id="descricao"
              value={descricao}
              onChange={(e) => setDescricao(e.target.value)}
              placeholder="Motivo da transferência..."
              maxLength={100}
            />
            <p className="text-xs text-gray-500">
              {descricao.length}/100 caracteres
            </p>
          </div>

          {/* Resumo da Transferência */}
          {natureza && contaOrigemInfo && contaDestinoInfo && valor && (
            <Alert className="bg-blue-50 border-blue-200">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium text-blue-800">Resumo da Transferência:</p>
                  <p className="text-sm text-blue-700">
                    <strong>De:</strong> {contaOrigemInfo.titular} ({contaOrigem})
                  </p>
                  <p className="text-sm text-blue-700">
                    <strong>Para:</strong> {contaDestinoInfo.titular} ({contaDestino})
                  </p>
                  <p className="text-sm text-blue-700">
                    <strong>Valor:</strong> {formatarValor(valor)} Kz
                  </p>
                  <p className="text-sm text-blue-700">
                    <strong>Moeda:</strong> {naturezas.find(n => n.value === natureza)?.label}
                  </p>
                  {descricao && (
                    <p className="text-sm text-blue-700">
                      <strong>Descrição:</strong> {descricao}
                    </p>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Botões de Ação */}
          <div className="flex gap-4 pt-4">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  disabled={!isFormValid || isSubmitting}
                  className="bg-primary hover:bg-primary/90 text-white px-8 flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    'Confirmar Transferência'
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmar Transferência</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja realizar esta transferência? Esta ação não pode ser desfeita.
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-2">
                      <p><strong>Valor:</strong> {formatarValor(valor)} Kz</p>
                      <p><strong>De:</strong> {contaOrigemInfo?.titular}</p>
                      <p><strong>Para:</strong> {contaDestinoInfo?.titular}</p>
                    </div>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={handleConfirmar}>
                    Confirmar
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <Button
              variant="outline"
              onClick={() => {
                setNatureza('');
                setContaOrigem('');
                setContaDestino('');
                setValor('');
                setDescricao('');
                setContaOrigemInfo(null);
                setContaDestinoInfo(null);
                setErrors({});
              }}
              className="px-8"
            >
              Limpar
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TransferenciaInterna;
