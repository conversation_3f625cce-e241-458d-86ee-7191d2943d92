import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { DoorO<PERSON>, Wallet, Calculator, CheckCircle, AlertCircle, Clock, User } from 'lucide-react';

interface FormErrors {
  numeroCaixa?: string;
  operador?: string;
  saldoInicial?: string;
  observacoes?: string;
}

interface DenominacaoForm {
  notas10000: number;
  notas5000: number;
  notas2000: number;
  notas1000: number;
  notas500: number;
  notas200: number;
  notas100: number;
  notas50: number;
  moedas10: number;
  moedas5: number;
  moedas1: number;
}

const AberturaCaixa = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const [formData, setFormData] = useState({
    numeroCaixa: '',
    operador: '',
    dataAbertura: new Date().toISOString().split('T')[0],
    horaAbertura: new Date().toLocaleTimeString('pt-AO', { hour: '2-digit', minute: '2-digit' }),
    saldoInicial: '',
    observacoes: ''
  });

  const [denominacoes, setDenominacoes] = useState<DenominacaoForm>({
    notas10000: 0,
    notas5000: 0,
    notas2000: 0,
    notas1000: 0,
    notas500: 0,
    notas200: 0,
    notas100: 0,
    notas50: 0,
    moedas10: 0,
    moedas5: 0,
    moedas1: 0
  });

  const calcularTotal = () => {
    return (
      denominacoes.notas10000 * 10000 +
      denominacoes.notas5000 * 5000 +
      denominacoes.notas2000 * 2000 +
      denominacoes.notas1000 * 1000 +
      denominacoes.notas500 * 500 +
      denominacoes.notas200 * 200 +
      denominacoes.notas100 * 100 +
      denominacoes.notas50 * 50 +
      denominacoes.moedas10 * 10 +
      denominacoes.moedas5 * 5 +
      denominacoes.moedas1 * 1
    );
  };

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 2
    }).format(valor);
  };

  const validarFormulario = () => {
    const newErrors: FormErrors = {};

    if (!formData.numeroCaixa) {
      newErrors.numeroCaixa = 'Número do caixa é obrigatório';
    }

    if (!formData.operador) {
      newErrors.operador = 'Operador é obrigatório';
    }

    const totalCalculado = calcularTotal();
    const saldoInformado = parseFloat(formData.saldoInicial) || 0;

    if (Math.abs(totalCalculado - saldoInformado) > 0.01) {
      newErrors.saldoInicial = 'Saldo inicial deve coincidir com o total das denominações';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleDenominacaoChange = (denominacao: keyof DenominacaoForm, value: string) => {
    const numericValue = parseInt(value) || 0;
    setDenominacoes(prev => ({ ...prev, [denominacao]: numericValue }));
    
    // Atualizar saldo inicial automaticamente
    const novasDenominacoes = { ...denominacoes, [denominacao]: numericValue };
    const total = Object.entries(novasDenominacoes).reduce((acc, [key, qty]) => {
      const valores = {
        notas10000: 10000, notas5000: 5000, notas2000: 2000, notas1000: 1000,
        notas500: 500, notas200: 200, notas100: 100, notas50: 50,
        moedas10: 10, moedas5: 5, moedas1: 1
      };
      return acc + (qty * valores[key as keyof typeof valores]);
    }, 0);
    
    setFormData(prev => ({ ...prev, saldoInicial: total.toString() }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validarFormulario()) {
      toast({
        title: "Erro de validação",
        description: "Por favor, corrija os erros no formulário",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simula abertura do caixa
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Caixa aberto com sucesso",
        description: `Caixa ${formData.numeroCaixa} foi aberto com saldo inicial de ${formatarMoeda(calcularTotal())}`,
      });

      // Limpar formulário após sucesso
      setFormData({
        numeroCaixa: '',
        operador: '',
        dataAbertura: new Date().toISOString().split('T')[0],
        horaAbertura: new Date().toLocaleTimeString('pt-AO', { hour: '2-digit', minute: '2-digit' }),
        saldoInicial: '',
        observacoes: ''
      });
      setDenominacoes({
        notas10000: 0, notas5000: 0, notas2000: 0, notas1000: 0,
        notas500: 0, notas200: 0, notas100: 0, notas50: 0,
        moedas10: 0, moedas5: 0, moedas1: 0
      });
      setErrors({});

    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao abrir o caixa. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalCalculado = calcularTotal();
  const isFormValid = formData.numeroCaixa && formData.operador && totalCalculado > 0;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <DoorOpen className="h-8 w-8 text-twins-primary" />
            Abertura do Caixa
          </h1>
          <p className="text-gray-600 dark:text-gray-400">Registar abertura de caixa com contagem inicial de valores</p>
        </div>
        <Button 
          onClick={handleSubmit}
          disabled={!isFormValid || isSubmitting}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          {isSubmitting ? 'Abrindo...' : 'Confirmar Abertura'}
        </Button>
      </div>

      {totalCalculado > 0 && (
        <Alert className="border-green-200 bg-green-50">
          <Calculator className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Total Calculado:</strong> {formatarMoeda(totalCalculado)}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Informações Básicas */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Informações da Abertura
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Número do Caixa */}
              <div className="space-y-2">
                <Label htmlFor="numeroCaixa">
                  Número do Caixa <span className="text-red-500">*</span>
                </Label>
                <Select value={formData.numeroCaixa} onValueChange={(value) => handleInputChange('numeroCaixa', value)}>
                  <SelectTrigger className={errors.numeroCaixa ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Selecione o caixa" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Caixa 01</SelectItem>
                    <SelectItem value="2">Caixa 02</SelectItem>
                    <SelectItem value="3">Caixa 03</SelectItem>
                    <SelectItem value="4">Caixa 04</SelectItem>
                    <SelectItem value="5">Caixa 05</SelectItem>
                    <SelectItem value="6">Caixa 06</SelectItem>
                  </SelectContent>
                </Select>
                {errors.numeroCaixa && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.numeroCaixa}
                  </p>
                )}
              </div>

              {/* Operador */}
              <div className="space-y-2">
                <Label htmlFor="operador">
                  Operador <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="operador"
                  value={formData.operador}
                  onChange={(e) => handleInputChange('operador', e.target.value)}
                  placeholder="Nome do operador"
                  className={errors.operador ? 'border-red-500' : ''}
                />
                {errors.operador && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.operador}
                  </p>
                )}
              </div>

              {/* Data e Hora */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dataAbertura" className="dark:text-gray-100">Data de Abertura</Label>
                  <Input
                    id="dataAbertura"
                    type="date"
                    value={formData.dataAbertura}
                    onChange={(e) => handleInputChange('dataAbertura', e.target.value)}
                    className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                    readOnly
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="horaAbertura" className="dark:text-gray-100">Hora de Abertura</Label>
                  <Input
                    id="horaAbertura"
                    value={formData.horaAbertura}
                    className="bg-gray-50 dark:bg-gray-700 dark:text-gray-100"
                    readOnly
                  />
                </div>
              </div>

              {/* Saldo Inicial */}
              <div className="space-y-2">
                <Label htmlFor="saldoInicial" className="dark:text-gray-100">
                  Saldo Inicial (Kz) <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="saldoInicial"
                  type="number"
                  step="0.01"
                  value={formData.saldoInicial}
                  onChange={(e) => handleInputChange('saldoInicial', e.target.value)}
                  placeholder="0,00"
                  className={`${errors.saldoInicial ? 'border-red-500' : ''} bg-gray-50 dark:bg-gray-700 dark:text-gray-100`}
                  readOnly
                />
                {errors.saldoInicial && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.saldoInicial}
                  </p>
                )}
              </div>

              {/* Observações */}
              <div className="space-y-2">
                <Label htmlFor="observacoes">Observações</Label>
                <Input
                  id="observacoes"
                  value={formData.observacoes}
                  onChange={(e) => handleInputChange('observacoes', e.target.value)}
                  placeholder="Observações adicionais (opcional)"
                />
              </div>
            </CardContent>
          </Card>

          {/* Denominações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                Contagem de Denominações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Notas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas</h4>
                {[
                  { key: 'notas5000', label: 'Notas de 5.000 Kz', valor: 5000 },
                  { key: 'notas2000', label: 'Notas de 2.000 Kz', valor: 2000 },
                  { key: 'notas1000', label: 'Notas de 1.000 Kz', valor: 1000 },
                  { key: 'notas500', label: 'Notas de 500 Kz', valor: 500 },
                  { key: 'notas200', label: 'Notas de 200 Kz', valor: 200 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Moedas */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-gray-700">Notas Pequenas</h4>
                {[
                  { key: 'notas200pequenas', label: 'Notas de 200 Kz', valor: 200 },
                  { key: 'notas100pequenas', label: 'Notas de 100 Kz', valor: 100 },
                  { key: 'notas50pequenas', label: 'Notas de 50 Kz', valor: 50 }
                ].map(({ key, label, valor }) => (
                  <div key={key} className="grid grid-cols-3 gap-2 items-center">
                    <Label className="text-xs">{label}</Label>
                    <Input
                      type="number"
                      min="0"
                      value={denominacoes[key as keyof DenominacaoForm]}
                      onChange={(e) => handleDenominacaoChange(key as keyof DenominacaoForm, e.target.value)}
                      placeholder="Qtd"
                      className="text-center"
                    />
                    <span className="text-xs text-gray-600 text-right">
                      {formatarMoeda(denominacoes[key as keyof DenominacaoForm] * valor)}
                    </span>
                  </div>
                ))}
              </div>

              {/* Total */}
              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total:</span>
                  <span className="text-lg font-bold text-twins-primary">
                    {formatarMoeda(totalCalculado)}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  );
};

export default AberturaCaixa;
