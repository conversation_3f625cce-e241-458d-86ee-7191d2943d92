import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { Briefcase, Building, FileText, Phone, Mail, MapPin } from 'lucide-react';

const AbrirContaEmpresa = () => {
  const { toast } = useToast();
  
  const [dadosEmpresa, setDadosEmpresa] = useState({
    nomeEmpresa: '',
    nif: '',
    numeroRegistoComercial: '',
    dataConstituicao: '',
    capitalSocial: '',
    tipoSociedade: '',
    actividadePrincipal: '',
    numeroFuncionarios: '',
    morada: '',
    provincia: '',
    municipio: '',
    telefone: '',
    email: '',
    website: '',
    representanteLegal: '',
    biRepresentante: '',
    cargoRepresentante: '',
    naturezaConta: '',
    tipoConta: '',
    finalidadeConta: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setDadosEmpresa(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    toast({
      title: "Conta Empresa Criada",
      description: "Conta empresarial registada com sucesso!",
    });
  };

  return (
    <div className="space-y-6 content-container">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Briefcase className="h-8 w-8" />
          Abertura de Conta Empresa
        </h1>
        <p className="text-gray-600 dark:text-gray-400">Registar nova conta empresarial</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Dados da Empresa */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Dados da Empresa
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="nome-empresa">Nome da Empresa *</Label>
              <Input
                id="nome-empresa"
                value={dadosEmpresa.nomeEmpresa}
                onChange={(e) => handleInputChange('nomeEmpresa', e.target.value)}
                placeholder="Nome completo da empresa"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="nif">NIF *</Label>
                <Input
                  id="nif"
                  value={dadosEmpresa.nif}
                  onChange={(e) => handleInputChange('nif', e.target.value)}
                  placeholder="Número de identificação fiscal"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="registo-comercial">Nº Registo Comercial *</Label>
                <Input
                  id="registo-comercial"
                  value={dadosEmpresa.numeroRegistoComercial}
                  onChange={(e) => handleInputChange('numeroRegistoComercial', e.target.value)}
                  placeholder="Número do registo comercial"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="data-constituicao">Data de Constituição *</Label>
                <Input
                  id="data-constituicao"
                  type="date"
                  value={dadosEmpresa.dataConstituicao}
                  onChange={(e) => handleInputChange('dataConstituicao', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="capital-social">Capital Social (Kz) *</Label>
                <Input
                  id="capital-social"
                  value={dadosEmpresa.capitalSocial}
                  onChange={(e) => handleInputChange('capitalSocial', e.target.value)}
                  placeholder="Valor do capital social"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipo-sociedade">Tipo de Sociedade *</Label>
              <Select value={dadosEmpresa.tipoSociedade} onValueChange={(value) => handleInputChange('tipoSociedade', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de sociedade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lda">Sociedade por Quotas (Lda)</SelectItem>
                  <SelectItem value="sa">Sociedade Anónima (SA)</SelectItem>
                  <SelectItem value="unipessoal">Sociedade Unipessoal</SelectItem>
                  <SelectItem value="cooperativa">Cooperativa</SelectItem>
                  <SelectItem value="outros">Outros</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="actividade-principal">Actividade Principal *</Label>
              <Textarea
                id="actividade-principal"
                value={dadosEmpresa.actividadePrincipal}
                onChange={(e) => handleInputChange('actividadePrincipal', e.target.value)}
                placeholder="Descreva a actividade principal da empresa"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="numero-funcionarios">Número de Funcionários</Label>
              <Select value={dadosEmpresa.numeroFuncionarios} onValueChange={(value) => handleInputChange('numeroFuncionarios', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o número de funcionários" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-5">1-5 funcionários</SelectItem>
                  <SelectItem value="6-20">6-20 funcionários</SelectItem>
                  <SelectItem value="21-50">21-50 funcionários</SelectItem>
                  <SelectItem value="51-100">51-100 funcionários</SelectItem>
                  <SelectItem value="100+">Mais de 100 funcionários</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Contactos e Localização */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Contactos e Localização
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="morada">Morada *</Label>
              <Textarea
                id="morada"
                value={dadosEmpresa.morada}
                onChange={(e) => handleInputChange('morada', e.target.value)}
                placeholder="Endereço completo da empresa"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="provincia">Província *</Label>
                <Select value={dadosEmpresa.provincia} onValueChange={(value) => handleInputChange('provincia', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a província" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="luanda">Luanda</SelectItem>
                    <SelectItem value="benguela">Benguela</SelectItem>
                    <SelectItem value="huambo">Huambo</SelectItem>
                    <SelectItem value="lobito">Lobito</SelectItem>
                    <SelectItem value="outras">Outras</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="municipio">Município *</Label>
                <Input
                  id="municipio"
                  value={dadosEmpresa.municipio}
                  onChange={(e) => handleInputChange('municipio', e.target.value)}
                  placeholder="Município"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="telefone">Telefone *</Label>
              <Input
                id="telefone"
                value={dadosEmpresa.telefone}
                onChange={(e) => handleInputChange('telefone', e.target.value)}
                placeholder="Telefone da empresa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={dadosEmpresa.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Email da empresa"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="website">Website</Label>
              <Input
                id="website"
                value={dadosEmpresa.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="www.exemplo.com"
              />
            </div>
          </CardContent>
        </Card>

        {/* Representante Legal */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Representante Legal
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="representante-legal">Nome Completo *</Label>
              <Input
                id="representante-legal"
                value={dadosEmpresa.representanteLegal}
                onChange={(e) => handleInputChange('representanteLegal', e.target.value)}
                placeholder="Nome do representante legal"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bi-representante">Bilhete de Identidade *</Label>
                <Input
                  id="bi-representante"
                  value={dadosEmpresa.biRepresentante}
                  onChange={(e) => handleInputChange('biRepresentante', e.target.value)}
                  placeholder="Número do BI"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cargo-representante">Cargo na Empresa *</Label>
                <Input
                  id="cargo-representante"
                  value={dadosEmpresa.cargoRepresentante}
                  onChange={(e) => handleInputChange('cargoRepresentante', e.target.value)}
                  placeholder="Ex: Administrador, Gerente"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Dados da Conta */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Dados da Conta
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="natureza-conta">Natureza da Conta *</Label>
              <Select value={dadosEmpresa.naturezaConta} onValueChange={(value) => handleInputChange('naturezaConta', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a natureza" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kwanza">Kwanza - AKZ</SelectItem>
                  <SelectItem value="euro">EURO - EUR</SelectItem>
                  <SelectItem value="dolar">US Dolar - USD</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipo-conta">Tipo de Conta *</Label>
              <Select value={dadosEmpresa.tipoConta} onValueChange={(value) => handleInputChange('tipoConta', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de conta" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="corrente">Conta Corrente</SelectItem>
                  <SelectItem value="deposito">Conta Depósito</SelectItem>
                  <SelectItem value="poupanca">Conta Poupança Empresarial</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="finalidade-conta">Finalidade da Conta *</Label>
              <Textarea
                id="finalidade-conta"
                value={dadosEmpresa.finalidadeConta}
                onChange={(e) => handleInputChange('finalidadeConta', e.target.value)}
                placeholder="Descreva a finalidade da conta bancária"
                rows={3}
              />
            </div>

            <div className="pt-4">
              <Button onClick={handleSubmit} className="w-full bg-primary hover:bg-primary/90">
                Criar Conta Empresa
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AbrirContaEmpresa;
