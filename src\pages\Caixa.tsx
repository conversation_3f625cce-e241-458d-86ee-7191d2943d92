
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Wallet, ArrowUpDown, TrendingUp, TrendingDown } from 'lucide-react';

const Caixa = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Caixa</h1>
          <p className="text-gray-600 dark:text-gray-400">Operações de caixa e movimentações diárias</p>
        </div>
        <Button className="flex items-center gap-2">
          <ArrowUpDown className="h-4 w-4" />
          Nova Operação
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo em Caixa</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">40.708.350 Kz</div>
            <p className="text-xs text-muted-foreground">Atualizado há 2 minutos</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Entradas Hoje</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">11.601.000 Kz</div>
            <p className="text-xs text-muted-foreground">48 operações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saídas Hoje</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">7.605.675 Kz</div>
            <p className="text-xs text-muted-foreground">32 operações</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saldo Líquido</CardTitle>
            <ArrowUpDown className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">+3.995.325 Kz</div>
            <p className="text-xs text-muted-foreground">Diferença do dia</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Últimas Operações</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">Depósito - Cliente 001234</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">14:32</div>
                </div>
                <div className="text-green-600 font-bold">+€ 2.500,00</div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">Levantamento - Cliente 005678</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">14:15</div>
                </div>
                <div className="text-red-600 font-bold">-€ 850,00</div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div>
                  <div className="font-medium dark:text-gray-100">Transferência - Cliente 009876</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">13:58</div>
                </div>
                <div className="text-blue-600 font-bold">€ 1.200,00</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Denominações em Caixa</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="dark:text-gray-100">Notas de 10.000 Kz</span>
                <span className="font-bold dark:text-gray-100">54 un. (540.000 Kz)</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dark:text-gray-100">Notas de 5.000 Kz</span>
                <span className="font-bold dark:text-gray-100">90 un. (450.000 Kz)</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dark:text-gray-100">Notas de 2.000 Kz</span>
                <span className="font-bold dark:text-gray-100">400 un. (800.000 Kz)</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dark:text-gray-100">Notas de 1.000 Kz</span>
                <span className="font-bold dark:text-gray-100">702 un. (702.000 Kz)</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="dark:text-gray-100">Notas de 500 Kz</span>
                <span className="font-bold dark:text-gray-100">842 un. (421.000 Kz)</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Outras denominações</span>
                <span className="font-bold">10.795.350 Kz</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Caixa;
