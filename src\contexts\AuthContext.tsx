import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  User, 
  AuthContextType, 
  LoginCredentials, 
  UserRole, 
  Permission,
  ROLE_PERMISSIONS 
} from '@/types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock de usuários para demonstração
const MOCK_USERS: User[] = [
  {
    id: '1',
    nome: 'Administra<PERSON>',
    email: '<EMAIL>',
    perfil: 'admin',
    balcao: 'Sede',
    telefone: '+244 923 456 789',
    status: 'ativo',
    criadoEm: new Date('2024-01-01')
  },
  {
    id: '2',
    nome: '<PERSON>',
    email: '<EMAIL>',
    perfil: 'gerente',
    balcao: 'Agência Central',
    telefone: '+244 923 456 790',
    status: 'ativo',
    criadoEm: new Date('2024-01-15')
  },
  {
    id: '3',
    nome: 'Maria Santos',
    email: '<EMAIL>',
    perfil: 'caixa',
    balcao: 'Agência Talatona',
    telefone: '+244 923 456 791',
    status: 'ativo',
    criadoEm: new Date('2024-02-01')
  },
  {
    id: '4',
    nome: 'Carlos Mendes',
    email: '<EMAIL>',
    perfil: 'tesoureiro',
    balcao: 'Sede',
    telefone: '+244 923 456 792',
    status: 'ativo',
    criadoEm: new Date('2024-02-15')
  }
];

// Mock de senhas (em produção, isso seria hasheado e verificado no backend)
const MOCK_PASSWORDS: { [email: string]: string } = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'gerente123',
  '<EMAIL>': 'caixa123',
  '<EMAIL>': 'tesoureiro123'
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Verificar se há usuário logado no localStorage ao inicializar
  useEffect(() => {
    const checkAuthState = () => {
      try {
        const savedUser = localStorage.getItem('twins-bank-user');
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          // Converter strings de data de volta para objetos Date
          if (userData.criadoEm) {
            userData.criadoEm = new Date(userData.criadoEm);
          }
          if (userData.ultimoLogin) {
            userData.ultimoLogin = new Date(userData.ultimoLogin);
          }
          setUser(userData);
        }
      } catch (error) {
        console.error('Erro ao recuperar dados do usuário:', error);
        localStorage.removeItem('twins-bank-user');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthState();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    setIsLoading(true);
    
    try {
      // Simular delay de rede
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Verificar credenciais
      const foundUser = MOCK_USERS.find(u => u.email === credentials.email);
      const expectedPassword = MOCK_PASSWORDS[credentials.email];
      
      if (!foundUser || expectedPassword !== credentials.senha) {
        throw new Error('Email ou senha incorretos');
      }
      
      if (foundUser.status !== 'ativo') {
        throw new Error('Usuário inativo ou bloqueado');
      }
      
      // Atualizar último login
      const userWithLogin = {
        ...foundUser,
        ultimoLogin: new Date()
      };
      
      setUser(userWithLogin);
      localStorage.setItem('twins-bank-user', JSON.stringify(userWithLogin));
      
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('twins-bank-user');
  };

  const getPermissions = (role: UserRole): Permission[] => {
    return ROLE_PERMISSIONS[role] || [];
  };

  const hasPermission = (module: string, action: string): boolean => {
    if (!user) return false;
    
    const permissions = getPermissions(user.perfil);
    const modulePermission = permissions.find(p => p.module === module);
    
    return modulePermission?.actions.includes(action) || false;
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false;
    
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.perfil);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    permissions: user ? getPermissions(user.perfil) : [],
    login,
    logout,
    hasPermission,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
